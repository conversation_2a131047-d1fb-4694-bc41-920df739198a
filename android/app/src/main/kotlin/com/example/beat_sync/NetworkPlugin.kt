package com.example.beat_sync

import android.content.Context
import android.net.wifi.WifiConfiguration
import android.net.wifi.WifiManager
import android.net.wifi.WifiNetworkSpecifier
import android.net.ConnectivityManager
import android.net.NetworkRequest
import android.net.NetworkCallback
import android.net.Network
import android.os.Build
import androidx.annotation.NonNull
import io.flutter.embedding.engine.plugins.FlutterPlugin
import io.flutter.plugin.common.MethodCall
import io.flutter.plugin.common.MethodChannel
import io.flutter.plugin.common.MethodChannel.MethodCallHandler
import io.flutter.plugin.common.MethodChannel.Result
import java.lang.reflect.Method

/** NetworkPlugin */
class NetworkPlugin: FlutterPlugin, MethodCallHandler {
    private lateinit var channel: MethodChannel
    private lateinit var context: Context
    private lateinit var wifiManager: WifiManager
    private lateinit var connectivityManager: ConnectivityManager

    override fun onAttachedToEngine(@NonNull flutterPluginBinding: FlutterPlugin.FlutterPluginBinding) {
        channel = MethodChannel(flutterPluginBinding.binaryMessenger, "beatsync/network")
        channel.setMethodCallHandler(this)
        context = flutterPluginBinding.applicationContext
        wifiManager = context.getSystemService(Context.WIFI_SERVICE) as WifiManager
        connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
    }

    override fun onMethodCall(@NonNull call: MethodCall, @NonNull result: Result) {
        when (call.method) {
            "createHotspot" -> {
                val ssid = call.argument<String>("ssid") ?: "BeatsyncHotspot"
                val password = call.argument<String>("password") ?: "sync12345"
                createHotspot(ssid, password, result)
            }
            "stopHotspot" -> {
                stopHotspot(result)
            }
            "scanWifiNetworks" -> {
                scanWifiNetworks(result)
            }
            "connectToWifi" -> {
                val ssid = call.argument<String>("ssid") ?: ""
                val password = call.argument<String>("password") ?: ""
                connectToWifi(ssid, password, result)
            }
            "showHotspotInstructions" -> {
                // For iOS compatibility - not used on Android
                result.success(true)
            }
            else -> {
                result.notImplemented()
            }
        }
    }

    private fun createHotspot(ssid: String, password: String, result: Result) {
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                // Android 8.0+ - Use WifiManager.LocalOnlyHotspotCallback
                createLocalOnlyHotspot(ssid, password, result)
            } else {
                // Legacy method for older Android versions
                createLegacyHotspot(ssid, password, result)
            }
        } catch (e: Exception) {
            result.error("HOTSPOT_ERROR", "Failed to create hotspot: ${e.message}", null)
        }
    }

    @androidx.annotation.RequiresApi(Build.VERSION_CODES.O)
    private fun createLocalOnlyHotspot(ssid: String, password: String, result: Result) {
        try {
            val callback = object : WifiManager.LocalOnlyHotspotCallback() {
                override fun onStarted(reservation: WifiManager.LocalOnlyHotspotReservation?) {
                    super.onStarted(reservation)
                    result.success(true)
                }

                override fun onStopped() {
                    super.onStopped()
                    // Hotspot stopped
                }

                override fun onFailed(reason: Int) {
                    super.onFailed(reason)
                    val errorMessage = when (reason) {
                        ERROR_GENERIC -> "Generic error"
                        ERROR_INCOMPATIBLE_MODE -> "Incompatible mode"
                        ERROR_NO_CHANNEL -> "No channel available"
                        ERROR_TETHERING_DISALLOWED -> "Tethering disallowed"
                        else -> "Unknown error: $reason"
                    }
                    result.error("HOTSPOT_ERROR", "Failed to start hotspot: $errorMessage", null)
                }
            }

            wifiManager.startLocalOnlyHotspot(callback, null)
        } catch (e: SecurityException) {
            result.error("PERMISSION_ERROR", "Location permission required for hotspot", null)
        } catch (e: Exception) {
            result.error("HOTSPOT_ERROR", "Failed to create local hotspot: ${e.message}", null)
        }
    }

    @Suppress("DEPRECATION")
    private fun createLegacyHotspot(ssid: String, password: String, result: Result) {
        try {
            // This method uses reflection to access hidden APIs
            // Note: This may not work on all devices and Android versions
            
            val wifiConfig = WifiConfiguration().apply {
                SSID = ssid
                preSharedKey = password
                allowedKeyManagement.set(WifiConfiguration.KeyMgmt.WPA_PSK)
                allowedAuthAlgorithms.set(WifiConfiguration.AuthAlgorithm.OPEN)
            }

            // Use reflection to access setWifiApEnabled method
            val method: Method = wifiManager.javaClass.getMethod(
                "setWifiApEnabled", 
                WifiConfiguration::class.java, 
                Boolean::class.javaPrimitiveType
            )
            
            val success = method.invoke(wifiManager, wifiConfig, true) as Boolean
            result.success(success)
            
        } catch (e: Exception) {
            result.error("HOTSPOT_ERROR", "Legacy hotspot creation failed: ${e.message}", null)
        }
    }

    private fun stopHotspot(result: Result) {
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                // For LocalOnlyHotspot, it stops automatically when the app is destroyed
                // or when the reservation is cancelled
                result.success(true)
            } else {
                // Legacy method
                stopLegacyHotspot(result)
            }
        } catch (e: Exception) {
            result.error("HOTSPOT_ERROR", "Failed to stop hotspot: ${e.message}", null)
        }
    }

    @Suppress("DEPRECATION")
    private fun stopLegacyHotspot(result: Result) {
        try {
            val method: Method = wifiManager.javaClass.getMethod(
                "setWifiApEnabled", 
                WifiConfiguration::class.java, 
                Boolean::class.javaPrimitiveType
            )
            
            val success = method.invoke(wifiManager, null, false) as Boolean
            result.success(success)
            
        } catch (e: Exception) {
            result.error("HOTSPOT_ERROR", "Failed to stop legacy hotspot: ${e.message}", null)
        }
    }

    private fun scanWifiNetworks(result: Result) {
        try {
            if (!wifiManager.isWifiEnabled) {
                result.error("WIFI_DISABLED", "WiFi is disabled", null)
                return
            }

            val scanResults = wifiManager.scanResults
            val networkNames = scanResults
                .filter { !it.SSID.isNullOrEmpty() }
                .map { it.SSID }
                .distinct()
                .sorted()

            result.success(networkNames)
        } catch (e: SecurityException) {
            result.error("PERMISSION_ERROR", "Location permission required for WiFi scan", null)
        } catch (e: Exception) {
            result.error("SCAN_ERROR", "Failed to scan WiFi networks: ${e.message}", null)
        }
    }

    private fun connectToWifi(ssid: String, password: String, result: Result) {
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                // Android 10+ - Use WifiNetworkSpecifier
                connectToWifiModern(ssid, password, result)
            } else {
                // Legacy method for older Android versions
                connectToWifiLegacy(ssid, password, result)
            }
        } catch (e: Exception) {
            result.error("WIFI_ERROR", "Failed to connect to WiFi: ${e.message}", null)
        }
    }

    @androidx.annotation.RequiresApi(Build.VERSION_CODES.Q)
    private fun connectToWifiModern(ssid: String, password: String, result: Result) {
        try {
            val specifier = WifiNetworkSpecifier.Builder()
                .setSsid(ssid)
                .setWpa2Passphrase(password)
                .build()

            val request = NetworkRequest.Builder()
                .addTransportType(android.net.NetworkCapabilities.TRANSPORT_WIFI)
                .setNetworkSpecifier(specifier)
                .build()

            val callback = object : NetworkCallback() {
                override fun onAvailable(network: Network) {
                    super.onAvailable(network)
                    result.success(true)
                }

                override fun onUnavailable() {
                    super.onUnavailable()
                    result.success(false)
                }
            }

            connectivityManager.requestNetwork(request, callback)
        } catch (e: Exception) {
            result.error("WIFI_ERROR", "Modern WiFi connection failed: ${e.message}", null)
        }
    }

    @Suppress("DEPRECATION")
    private fun connectToWifiLegacy(ssid: String, password: String, result: Result) {
        try {
            val wifiConfig = WifiConfiguration().apply {
                SSID = "\"$ssid\""
                preSharedKey = "\"$password\""
                allowedKeyManagement.set(WifiConfiguration.KeyMgmt.WPA_PSK)
            }

            val networkId = wifiManager.addNetwork(wifiConfig)
            if (networkId == -1) {
                result.success(false)
                return
            }

            val success = wifiManager.disconnect() && 
                         wifiManager.enableNetwork(networkId, true) && 
                         wifiManager.reconnect()
            
            result.success(success)
        } catch (e: Exception) {
            result.error("WIFI_ERROR", "Legacy WiFi connection failed: ${e.message}", null)
        }
    }

    override fun onDetachedFromEngine(@NonNull binding: FlutterPlugin.FlutterPluginBinding) {
        channel.setMethodCallHandler(null)
    }
}
