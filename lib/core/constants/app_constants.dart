/// Application-wide constants
class AppConstants {
  // BLE Configuration
  static const String serviceUuid = '4e4b4e4f-5445-534b-594e-4341524f4e45';
  static const String characteristicUuid = '4e4b4e4f-5445-534b-594e-4341524f4e46';
  static const String deviceName = 'BeatsyncMaster';
  static const String slaveDeviceName = 'BeatsyncSlave';

  // Network Configuration
  static const int udpPort = 12345;
  static const String hotspotSSID = 'BeatsyncHotspot';
  static const String hotspotPassword = 'sync12345';
  static const int webrtcPort = 8080;

  // Sync Configuration
  static const int maxJitterBufferSize = 20;
  static const int minJitterBufferSize = 3;
  static const int syncIntervalMs = 500;
  static const int maxSyncOffsetUs = 50000; // 50ms
  static const double driftCorrectionAlpha = 0.1;

  // Audio Configuration
  static const int ultrasonicFrequency = 19000; // Hz
  static const int sampleRate = 44100;
  static const double toneDurationMs = 100;
  static const int audioBufferSizeMs = 50;

  // Security Configuration
  static const String encryptionKey = 'beatsync12345678'; // 16-byte key for AES-128
  static const int keyDerivationIterations = 10000;
  static const int saltLength = 16;

  // UI Configuration
  static const int maxDevicesDisplayed = 10;
  static const int metricsUpdateIntervalMs = 100;
  static const int chartDataPoints = 100;

  // Error Messages
  static const String bleNotAvailable = 'Bluetooth is not available on this device';
  static const String blePermissionDenied = 'Bluetooth permission denied';
  static const String audioPermissionDenied = 'Audio permission denied';
  static const String networkError = 'Network connection error';
  static const String syncError = 'Synchronization error';
  static const String audioError = 'Audio playback error';

  // Default Audio URLs (for testing)
  static const String defaultAudioUrl = 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav';
  static const List<String> sampleAudioUrls = [
    'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav',
    'https://www.soundjay.com/misc/sounds/beep-07a.wav',
    'https://www.soundjay.com/misc/sounds/beep-10.wav',
  ];
}

/// Enumeration for connection types
enum ConnectionType {
  ble,
  hotspot,
  webrtc,
  ultrasonic,
  bluetoothAudio,
}

/// Enumeration for device roles
enum DeviceRole {
  master,
  slave,
  standalone,
}

/// Enumeration for sync status
enum SyncStatus {
  disconnected,
  connecting,
  connected,
  syncing,
  synced,
  error,
}

/// Enumeration for audio states
enum AudioState {
  stopped,
  loading,
  buffering,
  playing,
  paused,
  error,
}
