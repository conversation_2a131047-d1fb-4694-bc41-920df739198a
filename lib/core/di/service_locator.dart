import 'package:get_it/get_it.dart';
import 'package:logger/logger.dart';
import 'package:just_audio/just_audio.dart';
import 'package:flutter_sound/flutter_sound.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../services/ble_manager.dart';
import '../../services/sync_manager.dart';
import '../../services/audio_manager.dart';
import '../../services/webrtc_manager.dart';
import '../../services/ultrasonic_manager.dart';
import '../../services/security_manager.dart';
import '../../services/network_manager.dart';
import '../../data/repositories/settings_repository.dart';
import '../../data/repositories/device_repository.dart';

/// Global service locator instance
final GetIt sl = GetIt.instance;

/// Initialize all dependencies
Future<void> initializeDependencies() async {
  // Core services
  sl.registerLazySingleton<Logger>(() => Logger(
    printer: PrettyPrinter(
      methodCount: 2,
      errorMethodCount: 8,
      lineLength: 120,
      colors: true,
      printEmojis: true,
      printTime: true,
    ),
  ));

  // External dependencies
  final sharedPreferences = await SharedPreferences.getInstance();
  sl.registerSingleton<SharedPreferences>(sharedPreferences);

  // Audio players
  sl.registerFactory<AudioPlayer>(() => AudioPlayer());
  sl.registerFactory<FlutterSoundRecorder>(() => FlutterSoundRecorder());
  sl.registerFactory<FlutterSoundPlayer>(() => FlutterSoundPlayer());

  // Repositories
  sl.registerLazySingleton<SettingsRepository>(
    () => SettingsRepository(sl<SharedPreferences>()),
  );
  sl.registerLazySingleton<DeviceRepository>(
    () => DeviceRepository(),
  );

  // Core managers
  sl.registerLazySingleton<SecurityManager>(
    () => SecurityManager(sl<Logger>()),
  );

  sl.registerLazySingleton<BLEManager>(
    () => BLEManager(
      logger: sl<Logger>(),
      securityManager: sl<SecurityManager>(),
    ),
  );

  sl.registerLazySingleton<SyncManager>(
    () => SyncManager(
      logger: sl<Logger>(),
      securityManager: sl<SecurityManager>(),
      settingsRepository: sl<SettingsRepository>(),
    ),
  );

  sl.registerLazySingleton<AudioManager>(
    () => AudioManager(
      logger: sl<Logger>(),
      settingsRepository: sl<SettingsRepository>(),
    ),
  );

  sl.registerLazySingleton<WebRTCManager>(
    () => WebRTCManager(
      logger: sl<Logger>(),
      securityManager: sl<SecurityManager>(),
    ),
  );

  sl.registerLazySingleton<UltrasonicManager>(
    () => UltrasonicManager(
      logger: sl<Logger>(),
      securityManager: sl<SecurityManager>(),
    ),
  );

  sl.registerLazySingleton<NetworkManager>(
    () => NetworkManager(
      logger: sl<Logger>(),
      settingsRepository: sl<SettingsRepository>(),
    ),
  );
}

/// Clean up all dependencies
Future<void> disposeDependencies() async {
  await sl.reset();
}
