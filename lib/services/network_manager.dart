import 'dart:async';
import 'dart:io';
import 'package:logger/logger.dart';
import 'package:network_info_plus/network_info_plus.dart';
import 'package:flutter/services.dart';

import '../core/constants/app_constants.dart';
import '../data/repositories/settings_repository.dart';

/// Abstract interface for network operations
abstract class INetworkManager {
  Stream<NetworkStatus> get networkStatusStream;
  Future<bool> createHotspot();
  Future<bool> stopHotspot();
  Future<String?> getWifiIP();
  Future<String?> getWifiSSID();
  Future<bool> isConnectedToWifi();
  Future<List<String>> scanWifiNetworks();
  Future<bool> connectToWifi(String ssid, String password);
  NetworkStatus get currentStatus;
  void dispose();
}

/// Network status enumeration
enum NetworkStatus {
  disconnected,
  connecting,
  connectedWifi,
  connectedHotspot,
  hotspotActive,
  error,
}

/// Network Manager implementation for hotspot and WiFi operations
class NetworkManager implements INetworkManager {
  final Logger _logger;
  final SettingsRepository _settingsRepository;
  
  static const MethodChannel _channel = MethodChannel('beatsync/network');
  
  final StreamController<NetworkStatus> _networkStatusController = 
      StreamController<NetworkStatus>.broadcast();
  
  final NetworkInfo _networkInfo = NetworkInfo();
  NetworkStatus _currentStatus = NetworkStatus.disconnected;
  Timer? _statusCheckTimer;
  bool _isHotspotActive = false;

  NetworkManager({
    required Logger logger,
    required SettingsRepository settingsRepository,
  }) : _logger = logger,
       _settingsRepository = settingsRepository {
    _initializeNetworkManager();
  }

  void _initializeNetworkManager() {
    // Start periodic network status checking
    _statusCheckTimer = Timer.periodic(const Duration(seconds: 5), (timer) {
      _checkNetworkStatus();
    });
    
    // Initial status check
    _checkNetworkStatus();
  }

  @override
  Stream<NetworkStatus> get networkStatusStream => _networkStatusController.stream;

  @override
  NetworkStatus get currentStatus => _currentStatus;

  @override
  Future<bool> createHotspot() async {
    try {
      _logger.i('Creating WiFi hotspot');
      
      if (Platform.isAndroid) {
        return await _createAndroidHotspot();
      } else if (Platform.isIOS) {
        return await _createIOSHotspot();
      } else {
        _logger.w('Hotspot creation not supported on this platform');
        return false;
      }
    } catch (e) {
      _logger.e('Failed to create hotspot: $e');
      _updateStatus(NetworkStatus.error);
      return false;
    }
  }

  @override
  Future<bool> stopHotspot() async {
    try {
      _logger.i('Stopping WiFi hotspot');
      
      if (Platform.isAndroid) {
        final result = await _channel.invokeMethod('stopHotspot');
        _isHotspotActive = false;
        _updateStatus(NetworkStatus.disconnected);
        return result as bool;
      } else if (Platform.isIOS) {
        // iOS doesn't allow programmatic hotspot control
        _logger.i('iOS hotspot must be stopped manually');
        _isHotspotActive = false;
        _updateStatus(NetworkStatus.disconnected);
        return true;
      }
      
      return false;
    } catch (e) {
      _logger.e('Failed to stop hotspot: $e');
      return false;
    }
  }

  @override
  Future<String?> getWifiIP() async {
    try {
      return await _networkInfo.getWifiIP();
    } catch (e) {
      _logger.e('Failed to get WiFi IP: $e');
      return null;
    }
  }

  @override
  Future<String?> getWifiSSID() async {
    try {
      return await _networkInfo.getWifiName();
    } catch (e) {
      _logger.e('Failed to get WiFi SSID: $e');
      return null;
    }
  }

  @override
  Future<bool> isConnectedToWifi() async {
    try {
      final wifiName = await _networkInfo.getWifiName();
      return wifiName != null && wifiName.isNotEmpty;
    } catch (e) {
      _logger.e('Failed to check WiFi connection: $e');
      return false;
    }
  }

  @override
  Future<List<String>> scanWifiNetworks() async {
    try {
      if (Platform.isAndroid) {
        final networks = await _channel.invokeMethod('scanWifiNetworks');
        return List<String>.from(networks as List);
      } else {
        _logger.w('WiFi scanning not supported on this platform');
        return [];
      }
    } catch (e) {
      _logger.e('Failed to scan WiFi networks: $e');
      return [];
    }
  }

  @override
  Future<bool> connectToWifi(String ssid, String password) async {
    try {
      _logger.i('Connecting to WiFi: $ssid');
      _updateStatus(NetworkStatus.connecting);
      
      if (Platform.isAndroid) {
        final result = await _channel.invokeMethod('connectToWifi', {
          'ssid': ssid,
          'password': password,
        });
        
        if (result as bool) {
          _updateStatus(NetworkStatus.connectedWifi);
          return true;
        } else {
          _updateStatus(NetworkStatus.error);
          return false;
        }
      } else {
        _logger.w('Programmatic WiFi connection not supported on this platform');
        return false;
      }
    } catch (e) {
      _logger.e('Failed to connect to WiFi: $e');
      _updateStatus(NetworkStatus.error);
      return false;
    }
  }

  Future<bool> _createAndroidHotspot() async {
    try {
      final ssid = _settingsRepository.hotspotSSID;
      final password = _settingsRepository.hotspotPassword;
      
      final result = await _channel.invokeMethod('createHotspot', {
        'ssid': ssid,
        'password': password,
      });
      
      if (result as bool) {
        _isHotspotActive = true;
        _updateStatus(NetworkStatus.hotspotActive);
        _logger.i('Android hotspot created successfully');
        return true;
      } else {
        _updateStatus(NetworkStatus.error);
        return false;
      }
    } catch (e) {
      _logger.e('Failed to create Android hotspot: $e');
      return false;
    }
  }

  Future<bool> _createIOSHotspot() async {
    try {
      // iOS doesn't allow programmatic hotspot creation
      // We need to guide the user to enable it manually
      await _channel.invokeMethod('showHotspotInstructions', {
        'ssid': _settingsRepository.hotspotSSID,
        'password': _settingsRepository.hotspotPassword,
      });
      
      _logger.i('iOS hotspot instructions shown to user');
      _updateStatus(NetworkStatus.hotspotActive);
      return true;
    } catch (e) {
      _logger.e('Failed to show iOS hotspot instructions: $e');
      return false;
    }
  }

  void _checkNetworkStatus() async {
    try {
      if (_isHotspotActive) {
        _updateStatus(NetworkStatus.hotspotActive);
        return;
      }
      
      final isConnected = await isConnectedToWifi();
      if (isConnected) {
        final ssid = await getWifiSSID();
        if (ssid == _settingsRepository.hotspotSSID) {
          _updateStatus(NetworkStatus.connectedHotspot);
        } else {
          _updateStatus(NetworkStatus.connectedWifi);
        }
      } else {
        _updateStatus(NetworkStatus.disconnected);
      }
    } catch (e) {
      _logger.e('Failed to check network status: $e');
      _updateStatus(NetworkStatus.error);
    }
  }

  void _updateStatus(NetworkStatus newStatus) {
    if (_currentStatus != newStatus) {
      _currentStatus = newStatus;
      _networkStatusController.add(newStatus);
      _logger.d('Network status changed to: $newStatus');
    }
  }

  /// Get network information
  Future<Map<String, dynamic>> getNetworkInfo() async {
    try {
      final wifiName = await _networkInfo.getWifiName();
      final wifiIP = await _networkInfo.getWifiIP();
      final wifiBSSID = await _networkInfo.getWifiBSSID();
      final wifiSubmask = await _networkInfo.getWifiSubmask();
      final wifiGateway = await _networkInfo.getWifiGatewayIP();
      final wifiBroadcast = await _networkInfo.getWifiBroadcast();
      
      return {
        'wifiName': wifiName,
        'wifiIP': wifiIP,
        'wifiBSSID': wifiBSSID,
        'wifiSubmask': wifiSubmask,
        'wifiGateway': wifiGateway,
        'wifiBroadcast': wifiBroadcast,
        'isHotspotActive': _isHotspotActive,
        'status': _currentStatus.toString(),
      };
    } catch (e) {
      _logger.e('Failed to get network info: $e');
      return {'error': e.toString()};
    }
  }

  /// Check if device is connected to Beatsync hotspot
  Future<bool> isConnectedToBeatsyncHotspot() async {
    try {
      final ssid = await getWifiSSID();
      return ssid == _settingsRepository.hotspotSSID;
    } catch (e) {
      _logger.e('Failed to check Beatsync hotspot connection: $e');
      return false;
    }
  }

  /// Get hotspot configuration
  Map<String, String> getHotspotConfig() {
    return {
      'ssid': _settingsRepository.hotspotSSID,
      'password': _settingsRepository.hotspotPassword,
    };
  }

  /// Update hotspot configuration
  Future<void> updateHotspotConfig(String ssid, String password) async {
    await _settingsRepository.setHotspotSSID(ssid);
    await _settingsRepository.setHotspotPassword(password);
    _logger.i('Hotspot configuration updated');
  }

  /// Get network status display name
  String get statusDisplayName {
    switch (_currentStatus) {
      case NetworkStatus.disconnected:
        return 'Disconnected';
      case NetworkStatus.connecting:
        return 'Connecting';
      case NetworkStatus.connectedWifi:
        return 'Connected to WiFi';
      case NetworkStatus.connectedHotspot:
        return 'Connected to Hotspot';
      case NetworkStatus.hotspotActive:
        return 'Hotspot Active';
      case NetworkStatus.error:
        return 'Network Error';
    }
  }

  /// Check if network is available for sync
  bool get isNetworkAvailable => 
      _currentStatus == NetworkStatus.connectedWifi ||
      _currentStatus == NetworkStatus.connectedHotspot ||
      _currentStatus == NetworkStatus.hotspotActive;

  @override
  void dispose() {
    _logger.i('Disposing Network Manager');
    _statusCheckTimer?.cancel();
    _networkStatusController.close();
    
    if (_isHotspotActive) {
      stopHotspot();
    }
  }
}
