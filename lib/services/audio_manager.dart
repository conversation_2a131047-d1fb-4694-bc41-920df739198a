import 'dart:async';
import 'package:just_audio/just_audio.dart';
import 'package:logger/logger.dart';

import '../core/constants/app_constants.dart';
import '../data/repositories/settings_repository.dart';

/// Abstract interface for audio operations
abstract class IAudioManager {
  Stream<AudioState> get audioStateStream;
  Stream<Duration> get positionStream;
  Stream<Duration?> get durationStream;
  Future<void> loadAudio(String url);
  Future<void> play({int? offsetMicroseconds});
  Future<void> pause();
  Future<void> stop();
  Future<void> seek(Duration position);
  AudioState get currentState;
  Duration get currentPosition;
  Duration? get currentDuration;
  void dispose();
}

/// Audio Manager implementation for synchronized audio playback
class AudioManager implements IAudioManager {
  final Logger _logger;
  final SettingsRepository _settingsRepository;
  
  final AudioPlayer _audioPlayer = AudioPlayer();
  final StreamController<AudioState> _audioStateController = 
      StreamController<AudioState>.broadcast();
  
  AudioState _currentState = AudioState.stopped;
  Timer? _syncTimer;
  int? _scheduledPlayTime;
  bool _isScheduledPlayback = false;

  AudioManager({
    required Logger logger,
    required SettingsRepository settingsRepository,
  }) : _logger = logger,
       _settingsRepository = settingsRepository {
    _initializeAudioPlayer();
  }

  void _initializeAudioPlayer() {
    // Listen to player state changes
    _audioPlayer.playerStateStream.listen((playerState) {
      _updateAudioState(playerState);
    });

    // Listen to processing state changes
    _audioPlayer.processingStateStream.listen((processingState) {
      _handleProcessingStateChange(processingState);
    });

    // Handle audio interruptions
    _audioPlayer.playbackEventStream.listen((event) {
      _handlePlaybackEvent(event);
    });
  }

  @override
  Stream<AudioState> get audioStateStream => _audioStateController.stream;

  @override
  Stream<Duration> get positionStream => _audioPlayer.positionStream;

  @override
  Stream<Duration?> get durationStream => _audioPlayer.durationStream;

  @override
  AudioState get currentState => _currentState;

  @override
  Duration get currentPosition => _audioPlayer.position;

  @override
  Duration? get currentDuration => _audioPlayer.duration;

  @override
  Future<void> loadAudio(String url) async {
    try {
      _logger.i('Loading audio from: $url');
      _updateState(AudioState.loading);

      // Set audio source
      await _audioPlayer.setUrl(url);
      
      // Configure audio session for low latency
      await _configureAudioSession();
      
      _updateState(AudioState.stopped);
      _logger.i('Audio loaded successfully');
      
      // Add to URL history
      await _settingsRepository.addToAudioUrlHistory(url);
      
    } catch (e) {
      _logger.e('Failed to load audio: $e');
      _updateState(AudioState.error);
      rethrow;
    }
  }

  @override
  Future<void> play({int? offsetMicroseconds}) async {
    try {
      if (offsetMicroseconds != null) {
        // Scheduled playback for synchronization
        _schedulePlayback(offsetMicroseconds);
      } else {
        // Immediate playback
        await _audioPlayer.play();
        _logger.i('Audio playback started');
      }
    } catch (e) {
      _logger.e('Failed to start audio playback: $e');
      _updateState(AudioState.error);
      rethrow;
    }
  }

  @override
  Future<void> pause() async {
    try {
      _cancelScheduledPlayback();
      await _audioPlayer.pause();
      _logger.i('Audio playback paused');
    } catch (e) {
      _logger.e('Failed to pause audio playback: $e');
      rethrow;
    }
  }

  @override
  Future<void> stop() async {
    try {
      _cancelScheduledPlayback();
      await _audioPlayer.stop();
      _logger.i('Audio playback stopped');
    } catch (e) {
      _logger.e('Failed to stop audio playback: $e');
      rethrow;
    }
  }

  @override
  Future<void> seek(Duration position) async {
    try {
      await _audioPlayer.seek(position);
      _logger.d('Seeked to position: ${position.inMilliseconds}ms');
    } catch (e) {
      _logger.e('Failed to seek audio: $e');
      rethrow;
    }
  }

  /// Schedule playback at a specific time for synchronization
  void _schedulePlayback(int offsetMicroseconds) {
    _cancelScheduledPlayback();
    
    final now = DateTime.now().microsecondsSinceEpoch;
    final playTime = now + offsetMicroseconds;
    _scheduledPlayTime = playTime;
    _isScheduledPlayback = true;
    
    _logger.i('Scheduled playback in ${offsetMicroseconds / 1000}ms');
    
    // Use a high-frequency timer for precise timing
    _syncTimer = Timer.periodic(const Duration(milliseconds: 1), (timer) {
      final currentTime = DateTime.now().microsecondsSinceEpoch;
      
      if (currentTime >= playTime) {
        timer.cancel();
        _executeScheduledPlayback();
      }
    });
  }

  void _executeScheduledPlayback() async {
    if (!_isScheduledPlayback) return;
    
    try {
      await _audioPlayer.play();
      _isScheduledPlayback = false;
      _scheduledPlayTime = null;
      
      _logger.i('Executed scheduled playback');
    } catch (e) {
      _logger.e('Failed to execute scheduled playback: $e');
      _updateState(AudioState.error);
    }
  }

  void _cancelScheduledPlayback() {
    _syncTimer?.cancel();
    _syncTimer = null;
    _isScheduledPlayback = false;
    _scheduledPlayTime = null;
  }

  void _updateAudioState(PlayerState playerState) {
    AudioState newState;
    
    switch (playerState.processingState) {
      case ProcessingState.idle:
        newState = AudioState.stopped;
        break;
      case ProcessingState.loading:
        newState = AudioState.loading;
        break;
      case ProcessingState.buffering:
        newState = AudioState.buffering;
        break;
      case ProcessingState.ready:
        newState = playerState.playing ? AudioState.playing : AudioState.paused;
        break;
      case ProcessingState.completed:
        newState = AudioState.stopped;
        break;
    }
    
    _updateState(newState);
  }

  void _handleProcessingStateChange(ProcessingState processingState) {
    switch (processingState) {
      case ProcessingState.completed:
        _logger.i('Audio playback completed');
        _cancelScheduledPlayback();
        break;
      case ProcessingState.idle:
        _cancelScheduledPlayback();
        break;
      default:
        break;
    }
  }

  void _handlePlaybackEvent(PlaybackEvent event) {
    // Handle audio interruptions, buffer underruns, etc.
    if (event.processingState == ProcessingState.buffering) {
      _logger.w('Audio buffering detected');
    }
  }

  void _updateState(AudioState newState) {
    if (_currentState != newState) {
      _currentState = newState;
      _audioStateController.add(newState);
      _logger.d('Audio state changed to: $newState');
    }
  }

  Future<void> _configureAudioSession() async {
    try {
      // Configure audio session for low latency (platform-specific)
      // This would typically involve platform channels for native configuration
      
      // Set audio attributes for Android
      await _audioPlayer.setAudioAttributes(
        const AudioAttributes(
          androidContentType: AndroidContentType.music,
          androidUsage: AndroidUsage.media,
        ),
      );
      
      _logger.d('Audio session configured for low latency');
    } catch (e) {
      _logger.w('Failed to configure audio session: $e');
      // Continue without low-latency configuration
    }
  }

  /// Get current playback statistics
  Map<String, dynamic> getPlaybackStatistics() {
    return {
      'state': _currentState.toString(),
      'position': currentPosition.inMilliseconds,
      'duration': currentDuration?.inMilliseconds,
      'isScheduled': _isScheduledPlayback,
      'scheduledTime': _scheduledPlayTime,
      'bufferedPosition': _audioPlayer.bufferedPosition.inMilliseconds,
    };
  }

  /// Preload audio for faster playback
  Future<void> preloadAudio(String url) async {
    try {
      _logger.i('Preloading audio: $url');
      
      // Create a temporary player for preloading
      final preloadPlayer = AudioPlayer();
      await preloadPlayer.setUrl(url);
      await preloadPlayer.load();
      await preloadPlayer.dispose();
      
      _logger.i('Audio preloaded successfully');
    } catch (e) {
      _logger.w('Failed to preload audio: $e');
      // Continue without preloading
    }
  }

  /// Set playback volume
  Future<void> setVolume(double volume) async {
    try {
      await _audioPlayer.setVolume(volume.clamp(0.0, 1.0));
      _logger.d('Volume set to: $volume');
    } catch (e) {
      _logger.e('Failed to set volume: $e');
    }
  }

  /// Set playback speed
  Future<void> setSpeed(double speed) async {
    try {
      await _audioPlayer.setSpeed(speed.clamp(0.1, 3.0));
      _logger.d('Speed set to: $speed');
    } catch (e) {
      _logger.e('Failed to set speed: $e');
    }
  }

  /// Check if audio is loaded and ready
  bool get isReady => _audioPlayer.duration != null && _currentState != AudioState.error;

  /// Check if currently playing
  bool get isPlaying => _currentState == AudioState.playing;

  /// Check if scheduled playback is pending
  bool get hasScheduledPlayback => _isScheduledPlayback;

  @override
  void dispose() {
    _logger.i('Disposing Audio Manager');
    _cancelScheduledPlayback();
    _audioPlayer.dispose();
    _audioStateController.close();
  }
}
