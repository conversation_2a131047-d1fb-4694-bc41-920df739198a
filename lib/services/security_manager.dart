import 'dart:convert';
import 'dart:math';
import 'dart:typed_data';
import 'package:crypto/crypto.dart';
import 'package:pointycastle/export.dart';
import 'package:logger/logger.dart';

import '../core/constants/app_constants.dart';

/// Abstract interface for security operations
abstract class ISecurityManager {
  Future<String> encryptData(String data);
  String decryptData(String encryptedData);
  String generateHMAC(String data);
  bool verifyHMAC(String data, String hmac);
  Uint8List generateSalt();
  Uint8List deriveKey(String password, Uint8List salt);
}

/// Security Manager implementation for encryption and authentication
class SecurityManager implements ISecurityManager {
  final Logger _logger;
  late final Uint8List _key;
  late final SecureRandom _secureRandom;

  SecurityManager(this._logger) {
    _initializeSecurity();
  }

  void _initializeSecurity() {
    try {
      // Initialize secure random
      _secureRandom = SecureRandom('Fortuna');
      final seedSource = Random.secure();
      final seeds = <int>[];
      for (int i = 0; i < 32; i++) {
        seeds.add(seedSource.nextInt(256));
      }
      _secureRandom.seed(KeyParameter(Uint8List.fromList(seeds)));

      // Initialize default key (in production, this should be derived from user input)
      _key = Uint8List.fromList(utf8.encode(AppConstants.encryptionKey));
      
      _logger.i('Security Manager initialized successfully');
    } catch (e) {
      _logger.e('Failed to initialize Security Manager: $e');
      rethrow;
    }
  }

  @override
  Future<String> encryptData(String data) async {
    try {
      // Generate random IV
      final iv = _generateRandomBytes(16);
      
      // Create AES-GCM cipher
      final cipher = GCMBlockCipher(AESEngine());
      final params = AEADParameters(KeyParameter(_key), 128, iv);
      
      cipher.init(true, params);
      
      // Encrypt data
      final plaintext = utf8.encode(data);
      final ciphertext = cipher.process(Uint8List.fromList(plaintext));
      
      // Combine IV + ciphertext + auth tag
      final result = Uint8List(iv.length + ciphertext.length);
      result.setRange(0, iv.length, iv);
      result.setRange(iv.length, result.length, ciphertext);
      
      return base64Encode(result);
    } catch (e) {
      _logger.e('Failed to encrypt data: $e');
      rethrow;
    }
  }

  @override
  String decryptData(String encryptedData) {
    try {
      final data = base64Decode(encryptedData);
      
      // Extract IV and ciphertext
      final iv = data.sublist(0, 16);
      final ciphertext = data.sublist(16);
      
      // Create AES-GCM cipher
      final cipher = GCMBlockCipher(AESEngine());
      final params = AEADParameters(KeyParameter(_key), 128, iv);
      
      cipher.init(false, params);
      
      // Decrypt data
      final plaintext = cipher.process(ciphertext);
      
      return utf8.decode(plaintext);
    } catch (e) {
      _logger.e('Failed to decrypt data: $e');
      rethrow;
    }
  }

  @override
  String generateHMAC(String data) {
    try {
      final hmac = Hmac(sha256, _key);
      final digest = hmac.convert(utf8.encode(data));
      return digest.toString();
    } catch (e) {
      _logger.e('Failed to generate HMAC: $e');
      rethrow;
    }
  }

  @override
  bool verifyHMAC(String data, String hmac) {
    try {
      final expectedHmac = generateHMAC(data);
      return _constantTimeEquals(expectedHmac, hmac);
    } catch (e) {
      _logger.e('Failed to verify HMAC: $e');
      return false;
    }
  }

  @override
  Uint8List generateSalt() {
    return _generateRandomBytes(AppConstants.saltLength);
  }

  @override
  Uint8List deriveKey(String password, Uint8List salt) {
    try {
      final pbkdf2 = PBKDF2KeyDerivator(HMac(SHA256Digest(), 64));
      pbkdf2.init(Pbkdf2Parameters(salt, AppConstants.keyDerivationIterations, 32));
      
      return pbkdf2.process(utf8.encode(password));
    } catch (e) {
      _logger.e('Failed to derive key: $e');
      rethrow;
    }
  }

  /// Generate cryptographically secure random bytes
  Uint8List _generateRandomBytes(int length) {
    final bytes = Uint8List(length);
    for (int i = 0; i < length; i++) {
      bytes[i] = _secureRandom.nextUint8();
    }
    return bytes;
  }

  /// Constant-time string comparison to prevent timing attacks
  bool _constantTimeEquals(String a, String b) {
    if (a.length != b.length) return false;
    
    int result = 0;
    for (int i = 0; i < a.length; i++) {
      result |= a.codeUnitAt(i) ^ b.codeUnitAt(i);
    }
    return result == 0;
  }

  /// Encrypt sync packet with timestamp and sequence validation
  Future<String> encryptSyncPacket(Map<String, dynamic> packet) async {
    try {
      // Add timestamp and sequence for replay protection
      packet['timestamp'] = DateTime.now().microsecondsSinceEpoch;
      packet['nonce'] = _generateRandomBytes(8);
      
      final jsonData = jsonEncode(packet);
      final encryptedData = await encryptData(jsonData);
      final hmac = generateHMAC(encryptedData);
      
      return jsonEncode({
        'data': encryptedData,
        'hmac': hmac,
      });
    } catch (e) {
      _logger.e('Failed to encrypt sync packet: $e');
      rethrow;
    }
  }

  /// Decrypt and validate sync packet
  Map<String, dynamic>? decryptSyncPacket(String encryptedPacket) {
    try {
      final packetData = jsonDecode(encryptedPacket);
      final encryptedData = packetData['data'] as String;
      final hmac = packetData['hmac'] as String;
      
      // Verify HMAC
      if (!verifyHMAC(encryptedData, hmac)) {
        _logger.w('HMAC verification failed for sync packet');
        return null;
      }
      
      // Decrypt data
      final decryptedData = decryptData(encryptedData);
      final packet = jsonDecode(decryptedData) as Map<String, dynamic>;
      
      // Validate timestamp (reject packets older than 5 seconds)
      final timestamp = packet['timestamp'] as int?;
      if (timestamp != null) {
        final now = DateTime.now().microsecondsSinceEpoch;
        if (now - timestamp > 5000000) { // 5 seconds in microseconds
          _logger.w('Sync packet timestamp too old, rejecting');
          return null;
        }
      }
      
      return packet;
    } catch (e) {
      _logger.e('Failed to decrypt sync packet: $e');
      return null;
    }
  }

  /// Generate device-specific encryption key
  Future<void> generateDeviceKey(String deviceId, String passphrase) async {
    try {
      final salt = generateSalt();
      final combinedInput = '$deviceId:$passphrase';
      _key = deriveKey(combinedInput, salt);
      
      _logger.i('Generated device-specific encryption key');
    } catch (e) {
      _logger.e('Failed to generate device key: $e');
      rethrow;
    }
  }

  /// Rotate encryption key
  Future<void> rotateKey() async {
    try {
      final newKey = _generateRandomBytes(32);
      _key = newKey;
      
      _logger.i('Encryption key rotated successfully');
    } catch (e) {
      _logger.e('Failed to rotate encryption key: $e');
      rethrow;
    }
  }
}
