import 'package:flutter/foundation.dart';
import 'package:logger/logger.dart';

import '../../core/constants/app_constants.dart';
import '../../core/di/service_locator.dart';
import '../../data/models/device_info.dart';
import '../../data/models/sync_packet.dart';
import '../../data/repositories/device_repository.dart';
import '../../data/repositories/settings_repository.dart';
import '../../services/ble_manager.dart';
import '../../services/sync_manager.dart';
import '../../services/audio_manager.dart';
import '../../services/webrtc_manager.dart';
import '../../services/ultrasonic_manager.dart';
import '../../services/network_manager.dart';

/// Main application state provider
class AppStateProvider extends ChangeNotifier {
  final Logger _logger = sl<Logger>();
  final DeviceRepository _deviceRepository = sl<DeviceRepository>();
  final SettingsRepository _settingsRepository = sl<SettingsRepository>();
  final BLEManager _bleManager = sl<BLEManager>();
  final SyncManager _syncManager = sl<SyncManager>();
  final AudioManager _audioManager = sl<AudioManager>();
  final WebRTCManager _webrtcManager = sl<WebRTCManager>();
  final UltrasonicManager _ultrasonicManager = sl<UltrasonicManager>();
  final NetworkManager _networkManager = sl<NetworkManager>();

  // Current state
  DeviceRole _deviceRole = DeviceRole.standalone;
  ConnectionType _activeConnectionType = ConnectionType.ble;
  SyncStatus _syncStatus = SyncStatus.disconnected;
  AudioState _audioState = AudioState.stopped;
  String _statusMessage = 'Ready';
  bool _isInitialized = false;

  // Sync metrics
  SyncMetrics? _currentSyncMetrics;
  List<SyncMetrics> _syncMetricsHistory = [];
  int? _currentTimeOffset;

  // Getters
  DeviceRole get deviceRole => _deviceRole;
  ConnectionType get activeConnectionType => _activeConnectionType;
  SyncStatus get syncStatus => _syncStatus;
  AudioState get audioState => _audioState;
  String get statusMessage => _statusMessage;
  bool get isInitialized => _isInitialized;
  SyncMetrics? get currentSyncMetrics => _currentSyncMetrics;
  List<SyncMetrics> get syncMetricsHistory => List.unmodifiable(_syncMetricsHistory);
  int? get currentTimeOffset => _currentTimeOffset;

  // Device lists
  List<DeviceInfo> get discoveredDevices => _deviceRepository.devices;
  List<DeviceInfo> get connectedDevices => _deviceRepository.connectedDevices;
  List<DeviceInfo> get syncedDevices => _deviceRepository.syncedDevices;

  // Audio properties
  Duration get audioPosition => _audioManager.currentPosition;
  Duration? get audioDuration => _audioManager.currentDuration;
  bool get isAudioReady => _audioManager.isReady;
  bool get isAudioPlaying => _audioManager.isPlaying;

  AppStateProvider() {
    _initializeApp();
  }

  Future<void> _initializeApp() async {
    try {
      _logger.i('Initializing application state');
      _updateStatus('Initializing...');

      // Load settings
      _deviceRole = _settingsRepository.deviceRole;
      _activeConnectionType = _settingsRepository.preferredConnectionType;

      // Initialize managers
      await _webrtcManager.initialize();
      await _ultrasonicManager.initialize();

      // Set up listeners
      _setupListeners();

      _isInitialized = true;
      _updateStatus('Ready');
      _logger.i('Application state initialized successfully');

    } catch (e) {
      _logger.e('Failed to initialize application state: $e');
      _updateStatus('Initialization failed: $e');
    }
  }

  void _setupListeners() {
    // Device repository listeners
    _deviceRepository.devicesStream.listen((devices) {
      notifyListeners();
    });

    _deviceRepository.deviceUpdatesStream.listen((device) {
      notifyListeners();
    });

    // Sync manager listeners
    _syncManager.syncMetricsStream.listen((metrics) {
      _currentSyncMetrics = metrics;
      _syncMetricsHistory.add(metrics);
      
      // Keep only recent metrics
      if (_syncMetricsHistory.length > AppConstants.chartDataPoints) {
        _syncMetricsHistory.removeAt(0);
      }
      
      notifyListeners();
    });

    _syncManager.timeOffsetStream.listen((offset) {
      _currentTimeOffset = offset;
      notifyListeners();
    });

    // Audio manager listeners
    _audioManager.audioStateStream.listen((state) {
      _audioState = state;
      notifyListeners();
    });

    // Network manager listeners
    _networkManager.networkStatusStream.listen((status) {
      _updateStatus('Network: ${status.toString()}');
    });
  }

  /// Start as master device
  Future<void> startAsMaster() async {
    try {
      _logger.i('Starting as master device');
      _updateStatus('Starting as master...');
      
      _deviceRole = DeviceRole.master;
      await _settingsRepository.setDeviceRole(_deviceRole);

      // Start BLE advertising
      await _bleManager.startAdvertising();
      
      // Start sync broadcasting
      await _syncManager.startMasterSync();
      
      // Create hotspot if preferred
      if (_activeConnectionType == ConnectionType.hotspot) {
        await _networkManager.createHotspot();
      }
      
      // Start ultrasonic transmission if enabled
      if (_activeConnectionType == ConnectionType.ultrasonic) {
        await _ultrasonicManager.startTransmitting();
      }

      _syncStatus = SyncStatus.connected;
      _updateStatus('Master active');
      notifyListeners();

    } catch (e) {
      _logger.e('Failed to start as master: $e');
      _updateStatus('Failed to start as master: $e');
    }
  }

  /// Start as slave device
  Future<void> startAsSlave() async {
    try {
      _logger.i('Starting as slave device');
      _updateStatus('Starting as slave...');
      
      _deviceRole = DeviceRole.slave;
      await _settingsRepository.setDeviceRole(_deviceRole);

      // Start BLE scanning
      await _bleManager.startScanning();
      
      // Start sync listening
      await _syncManager.startSlaveSync();
      
      // Start ultrasonic listening if enabled
      if (_activeConnectionType == ConnectionType.ultrasonic) {
        await _ultrasonicManager.startListening();
      }

      _syncStatus = SyncStatus.connecting;
      _updateStatus('Searching for master...');
      notifyListeners();

    } catch (e) {
      _logger.e('Failed to start as slave: $e');
      _updateStatus('Failed to start as slave: $e');
    }
  }

  /// Stop synchronization
  Future<void> stopSync() async {
    try {
      _logger.i('Stopping synchronization');
      _updateStatus('Stopping...');

      await _bleManager.disconnect();
      await _syncManager.stopSync();
      await _audioManager.stop();
      await _networkManager.stopHotspot();
      await _ultrasonicManager.stopTransmitting();
      await _ultrasonicManager.stopListening();
      await _webrtcManager.close();

      _deviceRole = DeviceRole.standalone;
      _syncStatus = SyncStatus.disconnected;
      _currentTimeOffset = null;
      _currentSyncMetrics = null;
      
      _deviceRepository.clearDevices();
      
      _updateStatus('Stopped');
      notifyListeners();

    } catch (e) {
      _logger.e('Failed to stop sync: $e');
      _updateStatus('Failed to stop: $e');
    }
  }

  /// Connect to a specific device
  Future<void> connectToDevice(String deviceId) async {
    try {
      _logger.i('Connecting to device: $deviceId');
      _updateStatus('Connecting to device...');

      await _bleManager.connectToDevice(deviceId);
      
      // Update device status
      _deviceRepository.updateDeviceSyncStatus(deviceId, SyncStatus.connected);
      
      _updateStatus('Connected to device');
      notifyListeners();

    } catch (e) {
      _logger.e('Failed to connect to device: $e');
      _updateStatus('Failed to connect: $e');
    }
  }

  /// Load and play audio
  Future<void> loadAndPlayAudio(String url) async {
    try {
      _logger.i('Loading audio: $url');
      _updateStatus('Loading audio...');

      await _audioManager.loadAudio(url);
      
      if (_deviceRole == DeviceRole.master) {
        // Master starts playback immediately
        await _audioManager.play();
      } else if (_deviceRole == DeviceRole.slave && _currentTimeOffset != null) {
        // Slave schedules synchronized playback
        await _audioManager.play(offsetMicroseconds: _currentTimeOffset);
      }
      
      _updateStatus('Playing audio');
      notifyListeners();

    } catch (e) {
      _logger.e('Failed to load/play audio: $e');
      _updateStatus('Audio error: $e');
    }
  }

  /// Pause audio playback
  Future<void> pauseAudio() async {
    try {
      await _audioManager.pause();
      _updateStatus('Audio paused');
      notifyListeners();
    } catch (e) {
      _logger.e('Failed to pause audio: $e');
    }
  }

  /// Stop audio playback
  Future<void> stopAudio() async {
    try {
      await _audioManager.stop();
      _updateStatus('Audio stopped');
      notifyListeners();
    } catch (e) {
      _logger.e('Failed to stop audio: $e');
    }
  }

  /// Switch connection type
  Future<void> switchConnectionType(ConnectionType newType) async {
    try {
      _logger.i('Switching connection type to: $newType');
      
      final oldType = _activeConnectionType;
      _activeConnectionType = newType;
      await _settingsRepository.setPreferredConnectionType(newType);

      // Stop old connection
      switch (oldType) {
        case ConnectionType.ble:
          await _bleManager.disconnect();
          break;
        case ConnectionType.hotspot:
          await _networkManager.stopHotspot();
          break;
        case ConnectionType.webrtc:
          await _webrtcManager.close();
          break;
        case ConnectionType.ultrasonic:
          await _ultrasonicManager.stopTransmitting();
          await _ultrasonicManager.stopListening();
          break;
        case ConnectionType.bluetoothAudio:
          // Handle Bluetooth audio disconnection
          break;
      }

      // Start new connection
      switch (newType) {
        case ConnectionType.ble:
          if (_deviceRole == DeviceRole.master) {
            await _bleManager.startAdvertising();
          } else {
            await _bleManager.startScanning();
          }
          break;
        case ConnectionType.hotspot:
          if (_deviceRole == DeviceRole.master) {
            await _networkManager.createHotspot();
          }
          break;
        case ConnectionType.webrtc:
          await _webrtcManager.initialize();
          break;
        case ConnectionType.ultrasonic:
          if (_deviceRole == DeviceRole.master) {
            await _ultrasonicManager.startTransmitting();
          } else {
            await _ultrasonicManager.startListening();
          }
          break;
        case ConnectionType.bluetoothAudio:
          // Handle Bluetooth audio connection
          break;
      }

      _updateStatus('Switched to ${newType.toString()}');
      notifyListeners();

    } catch (e) {
      _logger.e('Failed to switch connection type: $e');
      _updateStatus('Connection switch failed: $e');
    }
  }

  /// Get sync statistics
  Map<String, dynamic> getSyncStatistics() {
    return _syncManager.getSyncStatistics();
  }

  /// Get audio statistics
  Map<String, dynamic> getAudioStatistics() {
    return _audioManager.getPlaybackStatistics();
  }

  /// Get network information
  Future<Map<String, dynamic>> getNetworkInfo() async {
    return await _networkManager.getNetworkInfo();
  }

  void _updateStatus(String message) {
    _statusMessage = message;
    _logger.d('Status: $message');
    notifyListeners();
  }

  @override
  void dispose() {
    _logger.i('Disposing AppStateProvider');
    stopSync();
    super.dispose();
  }
}
