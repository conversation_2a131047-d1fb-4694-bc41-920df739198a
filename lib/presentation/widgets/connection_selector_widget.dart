import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../core/constants/app_constants.dart';
import '../providers/app_state_provider.dart';

/// Widget for selecting connection type
class ConnectionSelectorWidget extends StatelessWidget {
  const ConnectionSelectorWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Consumer<AppStateProvider>(
      builder: (context, appState, child) {
        return Card(
          margin: EdgeInsets.zero,
          child: Padding(
            padding: const EdgeInsets.all(12.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Connection Type',
                  style: Theme.of(context).textTheme.labelMedium,
                ),
                const SizedBox(height: 8),
                
                Wrap(
                  spacing: 8.0,
                  runSpacing: 4.0,
                  children: ConnectionType.values.map((type) {
                    final isActive = appState.activeConnectionType == type;
                    final isAvailable = _isConnectionTypeAvailable(type, appState);
                    
                    return FilterChip(
                      label: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            _getConnectionTypeIcon(type),
                            size: 16,
                            color: isActive ? Colors.white : null,
                          ),
                          const SizedBox(width: 4),
                          Text(_getConnectionTypeDisplayName(type)),
                        ],
                      ),
                      selected: isActive,
                      onSelected: isAvailable ? (selected) {
                        if (selected && !isActive) {
                          appState.switchConnectionType(type);
                        }
                      } : null,
                      backgroundColor: isAvailable ? null : Colors.grey.shade200,
                      selectedColor: _getConnectionTypeColor(type),
                      checkmarkColor: Colors.white,
                      tooltip: _getConnectionTypeTooltip(type, isAvailable),
                    );
                  }).toList(),
                ),
                
                const SizedBox(height: 8),
                
                // Connection status info
                Row(
                  children: [
                    Icon(
                      _getConnectionStatusIcon(appState),
                      size: 16,
                      color: _getConnectionStatusColor(appState),
                    ),
                    const SizedBox(width: 4),
                    Expanded(
                      child: Text(
                        _getConnectionStatusText(appState),
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: _getConnectionStatusColor(appState),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  bool _isConnectionTypeAvailable(ConnectionType type, AppStateProvider appState) {
    switch (type) {
      case ConnectionType.ble:
        return true; // Always available
      case ConnectionType.hotspot:
        return appState.deviceRole == DeviceRole.master; // Only masters can create hotspots
      case ConnectionType.webrtc:
        return true; // Available for both master and slave
      case ConnectionType.ultrasonic:
        return true; // Available for both master and slave
      case ConnectionType.bluetoothAudio:
        return false; // Not implemented yet
    }
  }

  IconData _getConnectionTypeIcon(ConnectionType type) {
    switch (type) {
      case ConnectionType.ble:
        return Icons.bluetooth;
      case ConnectionType.hotspot:
        return Icons.wifi_tethering;
      case ConnectionType.webrtc:
        return Icons.web;
      case ConnectionType.ultrasonic:
        return Icons.graphic_eq;
      case ConnectionType.bluetoothAudio:
        return Icons.bluetooth_audio;
    }
  }

  String _getConnectionTypeDisplayName(ConnectionType type) {
    switch (type) {
      case ConnectionType.ble:
        return 'BLE';
      case ConnectionType.hotspot:
        return 'Hotspot';
      case ConnectionType.webrtc:
        return 'WebRTC';
      case ConnectionType.ultrasonic:
        return 'Ultrasonic';
      case ConnectionType.bluetoothAudio:
        return 'BT Audio';
    }
  }

  Color _getConnectionTypeColor(ConnectionType type) {
    switch (type) {
      case ConnectionType.ble:
        return Colors.blue;
      case ConnectionType.hotspot:
        return Colors.green;
      case ConnectionType.webrtc:
        return Colors.orange;
      case ConnectionType.ultrasonic:
        return Colors.purple;
      case ConnectionType.bluetoothAudio:
        return Colors.indigo;
    }
  }

  String _getConnectionTypeTooltip(ConnectionType type, bool isAvailable) {
    if (!isAvailable) {
      switch (type) {
        case ConnectionType.hotspot:
          return 'Only available for master devices';
        case ConnectionType.bluetoothAudio:
          return 'Not implemented yet';
        default:
          return 'Not available';
      }
    }

    switch (type) {
      case ConnectionType.ble:
        return 'Bluetooth Low Energy - Good for device discovery and basic sync';
      case ConnectionType.hotspot:
        return 'Wi-Fi Hotspot - Best performance for multiple devices';
      case ConnectionType.webrtc:
        return 'WebRTC - Peer-to-peer connection with NAT traversal';
      case ConnectionType.ultrasonic:
        return 'Ultrasonic - Works without network connection';
      case ConnectionType.bluetoothAudio:
        return 'Bluetooth Audio - Fallback for audio streaming';
    }
  }

  IconData _getConnectionStatusIcon(AppStateProvider appState) {
    switch (appState.syncStatus) {
      case SyncStatus.disconnected:
        return Icons.signal_wifi_off;
      case SyncStatus.connecting:
        return Icons.sync;
      case SyncStatus.connected:
        return Icons.signal_wifi_4_bar;
      case SyncStatus.syncing:
        return Icons.sync;
      case SyncStatus.synced:
        return Icons.sync_alt;
      case SyncStatus.error:
        return Icons.error;
    }
  }

  Color _getConnectionStatusColor(AppStateProvider appState) {
    switch (appState.syncStatus) {
      case SyncStatus.disconnected:
        return Colors.grey;
      case SyncStatus.connecting:
        return Colors.orange;
      case SyncStatus.connected:
        return Colors.blue;
      case SyncStatus.syncing:
        return Colors.orange;
      case SyncStatus.synced:
        return Colors.green;
      case SyncStatus.error:
        return Colors.red;
    }
  }

  String _getConnectionStatusText(AppStateProvider appState) {
    final connectionName = _getConnectionTypeDisplayName(appState.activeConnectionType);
    
    switch (appState.syncStatus) {
      case SyncStatus.disconnected:
        return 'Not connected via $connectionName';
      case SyncStatus.connecting:
        return 'Connecting via $connectionName...';
      case SyncStatus.connected:
        return 'Connected via $connectionName';
      case SyncStatus.syncing:
        return 'Syncing via $connectionName...';
      case SyncStatus.synced:
        return 'Synced via $connectionName';
      case SyncStatus.error:
        return 'Error with $connectionName connection';
    }
  }
}
