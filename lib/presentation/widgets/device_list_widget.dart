import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../core/constants/app_constants.dart';
import '../../data/models/device_info.dart';
import '../providers/app_state_provider.dart';

/// Widget displaying discovered and connected devices
class DeviceListWidget extends StatelessWidget {
  const DeviceListWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Consumer<AppStateProvider>(
      builder: (context, appState, child) {
        return RefreshIndicator(
          onRefresh: () async {
            // Refresh device list
            if (appState.deviceRole == DeviceRole.slave) {
              // Restart scanning for slaves
              await appState.stopSync();
              await appState.startAsSlave();
            }
          },
          child: ListView(
            padding: const EdgeInsets.all(16.0),
            children: [
              // Connected Devices Section
              if (appState.connectedDevices.isNotEmpty) ...[
                _buildSectionHeader(context, 'Connected Devices', Icons.link),
                ...appState.connectedDevices.map((device) => 
                  _buildDeviceCard(context, device, appState, isConnected: true)),
                const SizedBox(height: 16),
              ],
              
              // Discovered Devices Section
              _buildSectionHeader(context, 'Discovered Devices', Icons.search),
              if (appState.discoveredDevices.isEmpty) ...[
                _buildEmptyState(context, appState),
              ] else ...[
                ...appState.discoveredDevices
                    .where((device) => !appState.connectedDevices.contains(device))
                    .map((device) => _buildDeviceCard(context, device, appState)),
              ],
              
              const SizedBox(height: 16),
              
              // Sync Status Summary
              _buildSyncSummary(context, appState),
            ],
          ),
        );
      },
    );
  }

  Widget _buildSectionHeader(BuildContext context, String title, IconData icon) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Row(
        children: [
          Icon(icon, size: 20),
          const SizedBox(width: 8),
          Text(
            title,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDeviceCard(BuildContext context, DeviceInfo device, AppStateProvider appState, {bool isConnected = false}) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8.0),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: _getDeviceRoleColor(device.role),
          child: Icon(
            _getDeviceRoleIcon(device.role),
            color: Colors.white,
          ),
        ),
        title: Text(
          device.name,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('ID: ${device.id.substring(0, 8)}...'),
            Text('Connection: ${device.connectionTypeDisplayName}'),
            Text('Status: ${device.syncStatusDisplayName}'),
            if (device.signalStrength != null)
              Text('Signal: ${device.signalStrength}dBm'),
            if (device.ipAddress != null)
              Text('IP: ${device.ipAddress}'),
          ],
        ),
        trailing: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              _getSyncStatusIcon(device.syncStatus),
              color: _getSyncStatusColor(device.syncStatus),
            ),
            const SizedBox(height: 4),
            if (device.signalStrength != null)
              _buildSignalStrengthIndicator(device.signalStrength!),
          ],
        ),
        onTap: isConnected ? null : () {
          _showDeviceDetailsDialog(context, device, appState);
        },
      ),
    );
  }

  Widget _buildSignalStrengthIndicator(int signalStrength) {
    int bars = 0;
    if (signalStrength > -50) bars = 4;
    else if (signalStrength > -60) bars = 3;
    else if (signalStrength > -70) bars = 2;
    else if (signalStrength > -80) bars = 1;

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: List.generate(4, (index) {
        return Container(
          width: 3,
          height: 8 + (index * 2),
          margin: const EdgeInsets.symmetric(horizontal: 0.5),
          decoration: BoxDecoration(
            color: index < bars ? Colors.green : Colors.grey.shade300,
            borderRadius: BorderRadius.circular(1),
          ),
        );
      }),
    );
  }

  Widget _buildEmptyState(BuildContext context, AppStateProvider appState) {
    String message;
    IconData icon;
    
    switch (appState.deviceRole) {
      case DeviceRole.master:
        message = 'Advertising as master.\nWaiting for slaves to connect...';
        icon = Icons.wifi_tethering;
        break;
      case DeviceRole.slave:
        message = 'Scanning for master devices...\nMake sure master is advertising.';
        icon = Icons.search;
        break;
      case DeviceRole.standalone:
        message = 'Select Master or Slave mode\nto discover devices.';
        icon = Icons.info;
        break;
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(32.0),
        child: Column(
          children: [
            Icon(
              icon,
              size: 64,
              color: Colors.grey.shade400,
            ),
            const SizedBox(height: 16),
            Text(
              message,
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                color: Colors.grey.shade600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSyncSummary(BuildContext context, AppStateProvider appState) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Sync Summary',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 12),
            
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildSummaryItem(
                  context,
                  'Total',
                  '${appState.discoveredDevices.length}',
                  Icons.devices,
                ),
                _buildSummaryItem(
                  context,
                  'Connected',
                  '${appState.connectedDevices.length}',
                  Icons.link,
                ),
                _buildSummaryItem(
                  context,
                  'Synced',
                  '${appState.syncedDevices.length}',
                  Icons.sync_alt,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryItem(BuildContext context, String label, String value, IconData icon) {
    return Column(
      children: [
        Icon(icon, size: 24, color: Theme.of(context).primaryColor),
        const SizedBox(height: 4),
        Text(
          value,
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall,
        ),
      ],
    );
  }

  void _showDeviceDetailsDialog(BuildContext context, DeviceInfo device, AppStateProvider appState) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(device.name),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildDetailRow('Device ID', device.id),
              _buildDetailRow('Role', device.role.toString().split('.').last),
              _buildDetailRow('Connection', device.connectionTypeDisplayName),
              _buildDetailRow('Status', device.syncStatusDisplayName),
              if (device.signalStrength != null)
                _buildDetailRow('Signal Strength', '${device.signalStrength}dBm'),
              if (device.ipAddress != null)
                _buildDetailRow('IP Address', device.ipAddress!),
              if (device.port != null)
                _buildDetailRow('Port', device.port.toString()),
              _buildDetailRow('Last Seen', _formatDateTime(device.lastSeen)),
              if (device.capabilities != null) ...[
                const SizedBox(height: 8),
                const Text('Capabilities:', style: TextStyle(fontWeight: FontWeight.bold)),
                ...device.capabilities!.entries.map((entry) =>
                  _buildDetailRow(entry.key, entry.value.toString())),
              ],
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
          if (!device.isConnected && appState.deviceRole == DeviceRole.slave)
            ElevatedButton(
              onPressed: () {
                appState.connectToDevice(device.id);
                Navigator.pop(context);
              },
              child: const Text('Connect'),
            ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }

  String _formatDateTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);
    
    if (difference.inSeconds < 60) {
      return '${difference.inSeconds}s ago';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes}m ago';
    } else {
      return '${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
    }
  }

  Color _getDeviceRoleColor(DeviceRole role) {
    switch (role) {
      case DeviceRole.master:
        return Colors.blue;
      case DeviceRole.slave:
        return Colors.green;
      case DeviceRole.standalone:
        return Colors.grey;
    }
  }

  IconData _getDeviceRoleIcon(DeviceRole role) {
    switch (role) {
      case DeviceRole.master:
        return Icons.wifi_tethering;
      case DeviceRole.slave:
        return Icons.devices;
      case DeviceRole.standalone:
        return Icons.phone_android;
    }
  }

  IconData _getSyncStatusIcon(SyncStatus status) {
    switch (status) {
      case SyncStatus.disconnected:
        return Icons.signal_wifi_off;
      case SyncStatus.connecting:
        return Icons.sync;
      case SyncStatus.connected:
        return Icons.signal_wifi_4_bar;
      case SyncStatus.syncing:
        return Icons.sync;
      case SyncStatus.synced:
        return Icons.sync_alt;
      case SyncStatus.error:
        return Icons.error;
    }
  }

  Color _getSyncStatusColor(SyncStatus status) {
    switch (status) {
      case SyncStatus.disconnected:
        return Colors.grey;
      case SyncStatus.connecting:
        return Colors.orange;
      case SyncStatus.connected:
        return Colors.blue;
      case SyncStatus.syncing:
        return Colors.orange;
      case SyncStatus.synced:
        return Colors.green;
      case SyncStatus.error:
        return Colors.red;
    }
  }
}
