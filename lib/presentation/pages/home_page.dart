import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../core/constants/app_constants.dart';
import '../providers/app_state_provider.dart';
import '../widgets/device_list_widget.dart';
import '../widgets/sync_dashboard_widget.dart';
import '../widgets/audio_controls_widget.dart';
import '../widgets/connection_selector_widget.dart';

/// Main home page with tabbed interface
class HomePage extends StatefulWidget {
  const HomePage({Key? key}) : super(key: key);

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> with TickerProviderStateMixin {
  late TabController _tabController;
  int _currentIndex = 0;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _tabController.addListener(() {
      setState(() {
        _currentIndex = _tabController.index;
      });
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<AppStateProvider>(
      builder: (context, appState, child) {
        return Scaffold(
          appBar: AppBar(
            title: const Text('Beatsync'),
            backgroundColor: Theme.of(context).colorScheme.inversePrimary,
            bottom: TabBar(
              controller: _tabController,
              tabs: const [
                Tab(icon: Icon(Icons.dashboard), text: 'Dashboard'),
                Tab(icon: Icon(Icons.devices), text: 'Devices'),
                Tab(icon: Icon(Icons.music_note), text: 'Audio'),
                Tab(icon: Icon(Icons.settings), text: 'Settings'),
              ],
            ),
            actions: [
              // Connection type indicator
              Padding(
                padding: const EdgeInsets.all(8.0),
                child: Chip(
                  label: Text(_getConnectionTypeDisplayName(appState.activeConnectionType)),
                  backgroundColor: _getConnectionTypeColor(appState.activeConnectionType),
                ),
              ),
              // Sync status indicator
              Padding(
                padding: const EdgeInsets.all(8.0),
                child: Icon(
                  _getSyncStatusIcon(appState.syncStatus),
                  color: _getSyncStatusColor(appState.syncStatus),
                ),
              ),
            ],
          ),
          body: TabBarView(
            controller: _tabController,
            children: [
              // Dashboard Tab
              const SyncDashboardWidget(),
              
              // Devices Tab
              const DeviceListWidget(),
              
              // Audio Tab
              const AudioControlsWidget(),
              
              // Settings Tab
              _buildSettingsTab(appState),
            ],
          ),
          bottomNavigationBar: _buildBottomControls(appState),
          floatingActionButton: _buildFloatingActionButton(appState),
        );
      },
    );
  }

  Widget _buildBottomControls(AppStateProvider appState) {
    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        border: Border(
          top: BorderSide(
            color: Theme.of(context).dividerColor,
            width: 1.0,
          ),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Status message
          Text(
            appState.statusMessage,
            style: Theme.of(context).textTheme.bodyMedium,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          
          // Connection type selector
          const ConnectionSelectorWidget(),
          const SizedBox(height: 8),
          
          // Main action buttons
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              ElevatedButton.icon(
                onPressed: appState.deviceRole == DeviceRole.master ? null : () {
                  appState.startAsMaster();
                },
                icon: const Icon(Icons.wifi_tethering),
                label: const Text('Master'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: appState.deviceRole == DeviceRole.master 
                      ? Theme.of(context).colorScheme.primary 
                      : null,
                ),
              ),
              ElevatedButton.icon(
                onPressed: appState.deviceRole == DeviceRole.slave ? null : () {
                  appState.startAsSlave();
                },
                icon: const Icon(Icons.devices),
                label: const Text('Slave'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: appState.deviceRole == DeviceRole.slave 
                      ? Theme.of(context).colorScheme.primary 
                      : null,
                ),
              ),
              ElevatedButton.icon(
                onPressed: appState.syncStatus == SyncStatus.disconnected ? null : () {
                  appState.stopSync();
                },
                icon: const Icon(Icons.stop),
                label: const Text('Stop'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Theme.of(context).colorScheme.error,
                  foregroundColor: Theme.of(context).colorScheme.onError,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildFloatingActionButton(AppStateProvider appState) {
    if (_currentIndex != 2) return const SizedBox.shrink(); // Only show on Audio tab
    
    return FloatingActionButton(
      onPressed: () {
        if (appState.isAudioPlaying) {
          appState.pauseAudio();
        } else {
          // Show audio URL input dialog
          _showAudioUrlDialog(appState);
        }
      },
      child: Icon(
        appState.isAudioPlaying ? Icons.pause : Icons.play_arrow,
      ),
    );
  }

  Widget _buildSettingsTab(AppStateProvider appState) {
    return ListView(
      padding: const EdgeInsets.all(16.0),
      children: [
        // Device Role Section
        Card(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Device Role',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                const SizedBox(height: 8),
                SegmentedButton<DeviceRole>(
                  segments: const [
                    ButtonSegment(
                      value: DeviceRole.master,
                      label: Text('Master'),
                      icon: Icon(Icons.wifi_tethering),
                    ),
                    ButtonSegment(
                      value: DeviceRole.slave,
                      label: Text('Slave'),
                      icon: Icon(Icons.devices),
                    ),
                    ButtonSegment(
                      value: DeviceRole.standalone,
                      label: Text('Standalone'),
                      icon: Icon(Icons.phone_android),
                    ),
                  ],
                  selected: {appState.deviceRole},
                  onSelectionChanged: (Set<DeviceRole> selection) {
                    // Handle role change
                  },
                ),
              ],
            ),
          ),
        ),
        
        const SizedBox(height: 16),
        
        // Sync Settings Section
        Card(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Sync Settings',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                const SizedBox(height: 16),
                
                // Current sync metrics
                if (appState.currentSyncMetrics != null) ...[
                  _buildMetricRow('Time Offset', '${appState.currentSyncMetrics!.offset}μs'),
                  _buildMetricRow('Latency', '${appState.currentSyncMetrics!.latency}μs'),
                  _buildMetricRow('Accuracy', '${(appState.currentSyncMetrics!.accuracy * 100).toStringAsFixed(1)}%'),
                  _buildMetricRow('Drift', '${appState.currentSyncMetrics!.drift.toStringAsFixed(3)}'),
                ] else ...[
                  const Text('No sync metrics available'),
                ],
              ],
            ),
          ),
        ),
        
        const SizedBox(height: 16),
        
        // Network Settings Section
        Card(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Network Settings',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                const SizedBox(height: 16),
                
                ListTile(
                  title: const Text('Hotspot SSID'),
                  subtitle: const Text('BeatsyncHotspot'),
                  trailing: const Icon(Icons.edit),
                  onTap: () {
                    // Show SSID edit dialog
                  },
                ),
                
                ListTile(
                  title: const Text('Network Info'),
                  subtitle: const Text('Tap to view details'),
                  trailing: const Icon(Icons.info),
                  onTap: () {
                    _showNetworkInfoDialog(appState);
                  },
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildMetricRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label),
          Text(
            value,
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
        ],
      ),
    );
  }

  void _showAudioUrlDialog(AppStateProvider appState) {
    final controller = TextEditingController();
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Audio URL'),
        content: TextField(
          controller: controller,
          decoration: const InputDecoration(
            hintText: 'Enter audio URL...',
            border: OutlineInputBorder(),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              if (controller.text.isNotEmpty) {
                appState.loadAndPlayAudio(controller.text);
                Navigator.pop(context);
              }
            },
            child: const Text('Play'),
          ),
        ],
      ),
    );
  }

  void _showNetworkInfoDialog(AppStateProvider appState) async {
    final networkInfo = await appState.getNetworkInfo();
    
    if (!mounted) return;
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Network Information'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: networkInfo.entries.map((entry) {
              return Padding(
                padding: const EdgeInsets.symmetric(vertical: 4.0),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(
                      width: 100,
                      child: Text(
                        '${entry.key}:',
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      ),
                    ),
                    Expanded(
                      child: Text(entry.value?.toString() ?? 'N/A'),
                    ),
                  ],
                ),
              );
            }).toList(),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  String _getConnectionTypeDisplayName(ConnectionType type) {
    switch (type) {
      case ConnectionType.ble:
        return 'BLE';
      case ConnectionType.hotspot:
        return 'Hotspot';
      case ConnectionType.webrtc:
        return 'WebRTC';
      case ConnectionType.ultrasonic:
        return 'Ultrasonic';
      case ConnectionType.bluetoothAudio:
        return 'BT Audio';
    }
  }

  Color _getConnectionTypeColor(ConnectionType type) {
    switch (type) {
      case ConnectionType.ble:
        return Colors.blue.shade100;
      case ConnectionType.hotspot:
        return Colors.green.shade100;
      case ConnectionType.webrtc:
        return Colors.orange.shade100;
      case ConnectionType.ultrasonic:
        return Colors.purple.shade100;
      case ConnectionType.bluetoothAudio:
        return Colors.indigo.shade100;
    }
  }

  IconData _getSyncStatusIcon(SyncStatus status) {
    switch (status) {
      case SyncStatus.disconnected:
        return Icons.signal_wifi_off;
      case SyncStatus.connecting:
        return Icons.sync;
      case SyncStatus.connected:
        return Icons.signal_wifi_4_bar;
      case SyncStatus.syncing:
        return Icons.sync;
      case SyncStatus.synced:
        return Icons.sync_alt;
      case SyncStatus.error:
        return Icons.error;
    }
  }

  Color _getSyncStatusColor(SyncStatus status) {
    switch (status) {
      case SyncStatus.disconnected:
        return Colors.grey;
      case SyncStatus.connecting:
        return Colors.orange;
      case SyncStatus.connected:
        return Colors.blue;
      case SyncStatus.syncing:
        return Colors.orange;
      case SyncStatus.synced:
        return Colors.green;
      case SyncStatus.error:
        return Colors.red;
    }
  }
}
