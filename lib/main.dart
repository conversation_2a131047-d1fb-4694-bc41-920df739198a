import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_blue_plus/flutter_blue_plus.dart';
import 'package:flutter_sound/public/flutter_sound_player.dart';
import 'package:flutter_sound/public/flutter_sound_recorder.dart';
import 'package:flutter_webrtc/flutter_webrtc.dart';
import 'package:just_audio/just_audio.dart';
import 'package:provider/provider.dart';
import 'package:udp/udp.dart';

import 'core/di/service_locator.dart';
import 'presentation/providers/app_state_provider.dart';
import 'presentation/pages/home_page.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize dependencies
  await initializeDependencies();

  runApp(const BeatsyncApp());
}

class BeatsyncApp extends StatelessWidget {
  const BeatsyncApp({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (context) => AppStateProvider(),
      child: <PERSON>App(
        title: 'Beatsync',
        theme: ThemeData(
          colorScheme: ColorScheme.fromSeed(seedColor: Colors.blue),
          useMaterial3: true,
        ),
        darkTheme: ThemeData(
          colorScheme: ColorScheme.fromSeed(
            seedColor: Colors.blue,
            brightness: Brightness.dark,
          ),
          useMaterial3: true,
        ),
        home: const HomePage(),
      ),
    );
  }
}







  // WebRTC Initialization
  Future<void> _initWebRTC() async {
    final configuration = {
      'iceServers': [
        {'urls': 'stun:stun.l.google.com:19302'},
      ],
    };
    _peerConnection = await createPeerConnection(configuration);
    _dataChannel = await _peerConnection?.createDataChannel('sync', RTCDataChannelInit());
    _dataChannel?.onMessage = (message) {
      _handleSyncPacket(Datagram(message.binary, InternetAddress.anyIPv4, 12345));
    };
  }

  // BLE Device Discovery & Handshake
  Future<void> _startMaster() async {
    setState(() => _status = 'Advertising as Master');
    _isMaster = true;

    // Advertise BLE service
    await _ble.startAdvertising(
      services: [
        BluetoothService(
          uuid: _serviceUuid,
          characteristics: [
            BluetoothCharacteristic(
              uuid: _characteristicUuid,
              properties: CharacteristicProperties(
                read: true,
                write: true,
                notify: true,
              ),
            ),
          ],
        ),
      ],
    );

    // Setup hotspot (Android only)
    try {
      final wifiInfo = NetworkInfo();
      final hotspotConfig = {
        'SSID': 'BeatsyncHotspot',
        'password': 'sync12345',
      };
      // Note: Actual hotspot creation requires platform-specific code
      setState(() => _status = 'Hotspot Active');
      _startSyncBroadcast();
      _startUltrasonicSync();
    } catch (e) {
      setState(() => _status = 'Hotspot Error: $e');
      // Fallback to WebRTC or Bluetooth audio
      await _startWebRTC();
      if (_dataChannel == null) {
        _startBluetoothAudio();
      }
    }
  }

  Future<void> _startSlave() async {
    setState(() => _status = 'Scanning for Master');
    _isMaster = false;

    // Scan for master device
    _ble.scanResults.listen((results) {
      for (ScanResult r in results) {
        if (r.device.name == 'BeatsyncMaster') {
          _connectToMaster(r.device);
        }
      }
    });
    await _ble.startScan();

    // Start listening for ultrasonic signals
    _startUltrasonicListener();
  }

  Future<void> _connectToMaster(BluetoothDevice device) async {
    await device.connect();
    setState(() => _status = 'Connected to Master');
    
    // Discover services and get IP/port
    List<BluetoothService> services = await device.discoverServices();
    for (var service in services) {
      if (service.uuid == _serviceUuid) {
        for (var char in service.characteristics) {
          if (char.uuid == _characteristicUuid) {
            final ipPort = utf8.decode(await char.read());
            setState(() => _slaveAddress = ipPort);
            break;
          }
        }
      }
    }

    // Attempt WebRTC connection
    await _startWebRTC();
  }

  // Clock Synchronization
  void _startSyncBroadcast() {
    Timer.periodic(const Duration(milliseconds: 500), (timer) {
      if (!_isMaster) return;
      final packet = {
        'master_time': DateTime.now().microsecondsSinceEpoch,
        'sequence_id': _seq++,
      };
      final encrypted = _encryptPacket(jsonEncode(packet));
      _udpSocket?.send(
        encrypted,
        Endpoint.unicast(InternetAddress(_slaveAddress ?? '***************'), port: const Port(12345)),
      );
      _dataChannel?.send(RTCDataChannelMessage.fromBinary(encrypted));
    });
  }

  void _handleSyncPacket(Datagram datagram) {
    if (_isMaster) return;
    try {
      final decrypted = _decryptPacket(datagram.data);
      final packet = jsonDecode(decrypted);
      final t1 = packet['master_time'];
      final t2 = DateTime.now().microsecondsSinceEpoch;
      final offset = (t2 - t1) ~/ 2;

      // Jitter buffer management
      _jitterBuffer.add({'offset': offset, 'timestamp': t2});
      if (_jitterBuffer.length > _maxBufferSize) {
        _jitterBuffer.removeAt(0);
      }

      // Linear regression for clock drift
      _timeOffset = _calculateStableOffset();
      setState(() => _status = 'Sync Offset: $_timeOffset µs');
      _schedulePlayback();
    } catch (e) {
      setState(() => _status = 'Sync Error: $e');
    }
  }

  int _calculateStableOffset() {
    if (_jitterBuffer.length < 3) return _jitterBuffer.lastOrNull?['offset'] ?? 0;
    
    // Simple linear regression for drift correction
    final n = _jitterBuffer.length;
    double sumX = 0, sumY = 0, sumXY = 0, sumXX = 0;
    for (var i = 0; i < n; i++) {
      final x = i.toDouble();
      final y = _jitterBuffer[i]['offset'].toDouble();
      sumX += x;
      sumY += y;
      sumXY += x * y;
      sumXX += x * x;
    }
    final slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
    final intercept = (sumY - slope * sumX) / n;
    return (intercept + slope * (n - 1)).round();
  }

  // Ultrasonic Signaling
  void _startUltrasonicSync() async {
    if (!_isMaster) return;
    Timer.periodic(const Duration(seconds: 1), (timer) async {
      final packet = {
        'master_time': DateTime.now().microsecondsSinceEpoch,
        'sequence_id': _seq++,
      };
      final encoded = base64Encode(utf8.encode(jsonEncode(packet)));
      await _soundPlayer.startPlayerFromTrack(
        Track.fromBuffer(_generateTone(encoded)),
        whenFinished: () {},
      );
    });
  }

  void _startUltrasonicListener() async {
    await _recorder.startRecorder(
      toStream: _recorder.onProgress!.map((buffer) => _decodeTone(buffer)),
    );
  }

  List<int> _generateTone(String data) {
    // Generate 19kHz tone with data encoded in amplitude modulation
    final samples = <int>[];
    final sampleRate = 44100;
    final duration = 0.1; // 100ms per packet
    final samplesCount = (sampleRate * duration).round();
    
    for (var i = 0; i < samplesCount; i++) {
      final t = i / sampleRate;
      final amplitude = data.codeUnitAt(i % data.length) / 255.0;
      final sample = (sin(2 * pi * _ultrasonicFrequency * t) * amplitude * 32767).round();
      samples.add(sample);
    }
    return samples;
  }

  void _decodeTone(List<int> buffer) {
    // Simple amplitude demodulation
    final threshold = 10000;
    final bits = buffer.map((sample) => sample.abs() > threshold ? 1 : 0).join();
    final decoded = base64Decode(bits);
    _handleSyncPacket(Datagram(decoded, InternetAddress.anyIPv4, 12345));
  }

  // WebRTC Fallback
  Future<void> _startWebRTC() async {
    if (_dataChannel != null) {
      setState(() => _status = 'WebRTC Active');
      return;
    }
    try {
      final offer = await _peerConnection?.createOffer();
      await _peerConnection?.setLocalDescription(offer!);
      // Exchange SDP via BLE or other channel (implementation specific)
      setState(() => _status = 'WebRTC Connecting');
    } catch (e) {
      setState(() => _status = 'WebRTC Error: $e');
    }
  }

  // Audio Playback
  Future<void> _schedulePlayback() async {
    try {
      await _audioPlayer.setUrl('https://example.com/audio.mp3'); // Replace with actual audio source
      await _audioPlayer.seek(Duration(microseconds: _timeOffset ?? 0));
      await _audioPlayer.play();
      setState(() => _status = 'Playing Audio');
    } catch (e) {
      setState(() => _status = 'Playback Error: $e');
    }
  }

  Future<void> _startBluetoothAudio() async {
    // Fallback to Bluetooth A2DP streaming (platform-specific implementation needed)
    setState(() => _status = 'Using Bluetooth Audio Fallback');
  }

  // Security: AES encryption for UDP packets
SuppressWarnings('deprecation')
  List<int> _encryptPacket(String data) {
    final key = utf8.encode('beatsync12345678'); // 16-byte key for AES-128
    final hmac = Hmac(sha256, key);
    final digest = hmac.convert(utf8.encode(data));
    return utf8.encode(data + digest.toString());
  }

  String _decryptPacket(List<int> data) {
    final str = utf8.decode(data);
    final parts = str.split(Hmac(sha256, utf8.encode('beatsync12345678')).toString());
    return parts[0]; // Verify HMAC in production
  }

  @override
  void dispose() {
    _ble.stopScan();
    _udpSocket?.close();
    _peerConnection?.close();
    _dataChannel?.close();
    _audioPlayer.dispose();
    _recorder.closeRecorder();
    _soundPlayer.closePlayer();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Beatsync')),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text('Status: $_status'),
            ElevatedButton(
              onPressed: _startMaster,
              child: const Text('Start as Master'),
            ),
            ElevatedButton(
              onPressed: _startSlave,
              child: const Text('Start as Slave'),
            ),
          ],
        ),
      ),
    );
  }
}