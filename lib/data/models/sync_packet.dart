import 'dart:convert';

/// Represents a synchronization packet sent between devices
class SyncPacket {
  final int masterTime;
  final int sequenceId;
  final String deviceId;
  final int timestamp;
  final Map<String, dynamic>? metadata;

  const SyncPacket({
    required this.masterTime,
    required this.sequenceId,
    required this.deviceId,
    required this.timestamp,
    this.metadata,
  });

  /// Create SyncPacket from JSON
  factory SyncPacket.fromJson(Map<String, dynamic> json) {
    return SyncPacket(
      masterTime: json['master_time'] as int,
      sequenceId: json['sequence_id'] as int,
      deviceId: json['device_id'] as String,
      timestamp: json['timestamp'] as int,
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }

  /// Convert SyncPacket to JSON
  Map<String, dynamic> toJson() {
    return {
      'master_time': masterTime,
      'sequence_id': sequenceId,
      'device_id': deviceId,
      'timestamp': timestamp,
      if (metadata != null) 'metadata': metadata,
    };
  }

  /// Create SyncPacket from JSON string
  factory SyncPacket.fromJsonString(String jsonString) {
    return SyncPacket.fromJson(jsonDecode(jsonString));
  }

  /// Convert SyncPacket to JSON string
  String toJsonString() {
    return jsonEncode(toJson());
  }

  @override
  String toString() {
    return 'SyncPacket(masterTime: $masterTime, sequenceId: $sequenceId, deviceId: $deviceId, timestamp: $timestamp)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is SyncPacket &&
        other.masterTime == masterTime &&
        other.sequenceId == sequenceId &&
        other.deviceId == deviceId &&
        other.timestamp == timestamp;
  }

  @override
  int get hashCode {
    return masterTime.hashCode ^
        sequenceId.hashCode ^
        deviceId.hashCode ^
        timestamp.hashCode;
  }
}

/// Represents sync metrics for monitoring
class SyncMetrics {
  final int offset;
  final int latency;
  final double drift;
  final int jitterBufferSize;
  final DateTime timestamp;
  final double accuracy;

  const SyncMetrics({
    required this.offset,
    required this.latency,
    required this.drift,
    required this.jitterBufferSize,
    required this.timestamp,
    required this.accuracy,
  });

  factory SyncMetrics.fromJson(Map<String, dynamic> json) {
    return SyncMetrics(
      offset: json['offset'] as int,
      latency: json['latency'] as int,
      drift: (json['drift'] as num).toDouble(),
      jitterBufferSize: json['jitter_buffer_size'] as int,
      timestamp: DateTime.fromMillisecondsSinceEpoch(json['timestamp'] as int),
      accuracy: (json['accuracy'] as num).toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'offset': offset,
      'latency': latency,
      'drift': drift,
      'jitter_buffer_size': jitterBufferSize,
      'timestamp': timestamp.millisecondsSinceEpoch,
      'accuracy': accuracy,
    };
  }

  @override
  String toString() {
    return 'SyncMetrics(offset: ${offset}μs, latency: ${latency}μs, drift: ${drift.toStringAsFixed(3)}, accuracy: ${(accuracy * 100).toStringAsFixed(1)}%)';
  }
}
