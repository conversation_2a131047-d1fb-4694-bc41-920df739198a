import '../../core/constants/app_constants.dart';

/// Represents information about a connected device
class DeviceInfo {
  final String id;
  final String name;
  final DeviceRole role;
  final ConnectionType connectionType;
  final SyncStatus syncStatus;
  final String? ipAddress;
  final int? port;
  final DateTime lastSeen;
  final int? signalStrength;
  final Map<String, dynamic>? capabilities;

  const DeviceInfo({
    required this.id,
    required this.name,
    required this.role,
    required this.connectionType,
    required this.syncStatus,
    this.ipAddress,
    this.port,
    required this.lastSeen,
    this.signalStrength,
    this.capabilities,
  });

  /// Create DeviceInfo from JSON
  factory DeviceInfo.fromJson(Map<String, dynamic> json) {
    return DeviceInfo(
      id: json['id'] as String,
      name: json['name'] as String,
      role: DeviceRole.values[json['role'] as int],
      connectionType: ConnectionType.values[json['connection_type'] as int],
      syncStatus: SyncStatus.values[json['sync_status'] as int],
      ipAddress: json['ip_address'] as String?,
      port: json['port'] as int?,
      lastSeen: DateTime.fromMillisecondsSinceEpoch(json['last_seen'] as int),
      signalStrength: json['signal_strength'] as int?,
      capabilities: json['capabilities'] as Map<String, dynamic>?,
    );
  }

  /// Convert DeviceInfo to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'role': role.index,
      'connection_type': connectionType.index,
      'sync_status': syncStatus.index,
      if (ipAddress != null) 'ip_address': ipAddress,
      if (port != null) 'port': port,
      'last_seen': lastSeen.millisecondsSinceEpoch,
      if (signalStrength != null) 'signal_strength': signalStrength,
      if (capabilities != null) 'capabilities': capabilities,
    };
  }

  /// Create a copy with updated fields
  DeviceInfo copyWith({
    String? id,
    String? name,
    DeviceRole? role,
    ConnectionType? connectionType,
    SyncStatus? syncStatus,
    String? ipAddress,
    int? port,
    DateTime? lastSeen,
    int? signalStrength,
    Map<String, dynamic>? capabilities,
  }) {
    return DeviceInfo(
      id: id ?? this.id,
      name: name ?? this.name,
      role: role ?? this.role,
      connectionType: connectionType ?? this.connectionType,
      syncStatus: syncStatus ?? this.syncStatus,
      ipAddress: ipAddress ?? this.ipAddress,
      port: port ?? this.port,
      lastSeen: lastSeen ?? this.lastSeen,
      signalStrength: signalStrength ?? this.signalStrength,
      capabilities: capabilities ?? this.capabilities,
    );
  }

  /// Check if device is connected
  bool get isConnected => syncStatus != SyncStatus.disconnected && syncStatus != SyncStatus.error;

  /// Check if device is synced
  bool get isSynced => syncStatus == SyncStatus.synced;

  /// Get connection type display name
  String get connectionTypeDisplayName {
    switch (connectionType) {
      case ConnectionType.ble:
        return 'Bluetooth LE';
      case ConnectionType.hotspot:
        return 'Wi-Fi Hotspot';
      case ConnectionType.webrtc:
        return 'WebRTC';
      case ConnectionType.ultrasonic:
        return 'Ultrasonic';
      case ConnectionType.bluetoothAudio:
        return 'Bluetooth Audio';
    }
  }

  /// Get sync status display name
  String get syncStatusDisplayName {
    switch (syncStatus) {
      case SyncStatus.disconnected:
        return 'Disconnected';
      case SyncStatus.connecting:
        return 'Connecting';
      case SyncStatus.connected:
        return 'Connected';
      case SyncStatus.syncing:
        return 'Syncing';
      case SyncStatus.synced:
        return 'Synced';
      case SyncStatus.error:
        return 'Error';
    }
  }

  @override
  String toString() {
    return 'DeviceInfo(id: $id, name: $name, role: $role, connectionType: $connectionType, syncStatus: $syncStatus)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is DeviceInfo && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
