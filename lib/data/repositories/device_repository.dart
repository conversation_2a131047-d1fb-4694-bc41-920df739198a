import 'dart:async';
import 'dart:collection';
import '../models/device_info.dart';
import '../../core/constants/app_constants.dart';

/// Repository for managing discovered and connected devices
class DeviceRepository {
  final Map<String, DeviceInfo> _devices = {};
  final StreamController<List<DeviceInfo>> _devicesController = StreamController<List<DeviceInfo>>.broadcast();
  final StreamController<DeviceInfo> _deviceUpdatesController = StreamController<DeviceInfo>.broadcast();

  /// Stream of all devices
  Stream<List<DeviceInfo>> get devicesStream => _devicesController.stream;

  /// Stream of device updates
  Stream<DeviceInfo> get deviceUpdatesStream => _deviceUpdatesController.stream;

  /// Get all devices as a list
  List<DeviceInfo> get devices => UnmodifiableListView(_devices.values);

  /// Get connected devices
  List<DeviceInfo> get connectedDevices => devices.where((device) => device.isConnected).toList();

  /// Get synced devices
  List<DeviceInfo> get syncedDevices => devices.where((device) => device.isSynced).toList();

  /// Get master devices
  List<DeviceInfo> get masterDevices => devices.where((device) => device.role == DeviceRole.master).toList();

  /// Get slave devices
  List<DeviceInfo> get slaveDevices => devices.where((device) => device.role == DeviceRole.slave).toList();

  /// Add or update a device
  void addOrUpdateDevice(DeviceInfo device) {
    final existingDevice = _devices[device.id];
    
    if (existingDevice != null) {
      // Update existing device
      final updatedDevice = existingDevice.copyWith(
        name: device.name,
        role: device.role,
        connectionType: device.connectionType,
        syncStatus: device.syncStatus,
        ipAddress: device.ipAddress,
        port: device.port,
        lastSeen: device.lastSeen,
        signalStrength: device.signalStrength,
        capabilities: device.capabilities,
      );
      _devices[device.id] = updatedDevice;
      _deviceUpdatesController.add(updatedDevice);
    } else {
      // Add new device
      _devices[device.id] = device;
      _deviceUpdatesController.add(device);
    }
    
    _notifyDevicesChanged();
  }

  /// Remove a device
  void removeDevice(String deviceId) {
    final device = _devices.remove(deviceId);
    if (device != null) {
      _notifyDevicesChanged();
    }
  }

  /// Get device by ID
  DeviceInfo? getDevice(String deviceId) {
    return _devices[deviceId];
  }

  /// Update device sync status
  void updateDeviceSyncStatus(String deviceId, SyncStatus status) {
    final device = _devices[deviceId];
    if (device != null) {
      final updatedDevice = device.copyWith(
        syncStatus: status,
        lastSeen: DateTime.now(),
      );
      _devices[deviceId] = updatedDevice;
      _deviceUpdatesController.add(updatedDevice);
      _notifyDevicesChanged();
    }
  }

  /// Update device connection type
  void updateDeviceConnectionType(String deviceId, ConnectionType connectionType) {
    final device = _devices[deviceId];
    if (device != null) {
      final updatedDevice = device.copyWith(
        connectionType: connectionType,
        lastSeen: DateTime.now(),
      );
      _devices[deviceId] = updatedDevice;
      _deviceUpdatesController.add(updatedDevice);
      _notifyDevicesChanged();
    }
  }

  /// Update device signal strength
  void updateDeviceSignalStrength(String deviceId, int signalStrength) {
    final device = _devices[deviceId];
    if (device != null) {
      final updatedDevice = device.copyWith(
        signalStrength: signalStrength,
        lastSeen: DateTime.now(),
      );
      _devices[deviceId] = updatedDevice;
      _deviceUpdatesController.add(updatedDevice);
      _notifyDevicesChanged();
    }
  }

  /// Clear all devices
  void clearDevices() {
    _devices.clear();
    _notifyDevicesChanged();
  }

  /// Remove stale devices (not seen for more than 30 seconds)
  void removeStaleDevices() {
    final now = DateTime.now();
    final staleDeviceIds = <String>[];
    
    for (final device in _devices.values) {
      if (now.difference(device.lastSeen).inSeconds > 30) {
        staleDeviceIds.add(device.id);
      }
    }
    
    for (final deviceId in staleDeviceIds) {
      _devices.remove(deviceId);
    }
    
    if (staleDeviceIds.isNotEmpty) {
      _notifyDevicesChanged();
    }
  }

  /// Get devices by connection type
  List<DeviceInfo> getDevicesByConnectionType(ConnectionType connectionType) {
    return devices.where((device) => device.connectionType == connectionType).toList();
  }

  /// Get devices by role
  List<DeviceInfo> getDevicesByRole(DeviceRole role) {
    return devices.where((device) => device.role == role).toList();
  }

  /// Check if any master device is available
  bool get hasMasterDevice => masterDevices.isNotEmpty;

  /// Check if any device is synced
  bool get hasyncedDevice => syncedDevices.isNotEmpty;

  /// Get the primary master device (first connected master)
  DeviceInfo? get primaryMasterDevice {
    final masters = masterDevices.where((device) => device.isConnected).toList();
    return masters.isNotEmpty ? masters.first : null;
  }

  void _notifyDevicesChanged() {
    _devicesController.add(devices);
  }

  /// Dispose resources
  void dispose() {
    _devicesController.close();
    _deviceUpdatesController.close();
  }
}
