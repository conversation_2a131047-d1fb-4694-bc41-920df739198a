import 'package:shared_preferences/shared_preferences.dart';
import '../../core/constants/app_constants.dart';

/// Repository for managing app settings and preferences
class SettingsRepository {
  final SharedPreferences _prefs;

  SettingsRepository(this._prefs);

  // Keys for settings
  static const String _keyDeviceRole = 'device_role';
  static const String _keyPreferredConnection = 'preferred_connection';
  static const String _keyAudioUrl = 'audio_url';
  static const String _keyJitterBufferSize = 'jitter_buffer_size';
  static const String _keySyncInterval = 'sync_interval';
  static const String _keyUltrasonicFrequency = 'ultrasonic_frequency';
  static const String _keyEncryptionEnabled = 'encryption_enabled';
  static const String _keyAutoReconnect = 'auto_reconnect';
  static const String _keyDarkMode = 'dark_mode';
  static const String _keyDeviceName = 'device_name';
  static const String _keyHotspotSSID = 'hotspot_ssid';
  static const String _keyHotspotPassword = 'hotspot_password';

  // Device Role
  DeviceRole get deviceRole {
    final index = _prefs.getInt(_keyDeviceRole) ?? DeviceRole.standalone.index;
    return DeviceRole.values[index];
  }

  Future<bool> setDeviceRole(DeviceRole role) {
    return _prefs.setInt(_keyDeviceRole, role.index);
  }

  // Preferred Connection Type
  ConnectionType get preferredConnectionType {
    final index = _prefs.getInt(_keyPreferredConnection) ?? ConnectionType.ble.index;
    return ConnectionType.values[index];
  }

  Future<bool> setPreferredConnectionType(ConnectionType type) {
    return _prefs.setInt(_keyPreferredConnection, type.index);
  }

  // Audio URL
  String get audioUrl {
    return _prefs.getString(_keyAudioUrl) ?? AppConstants.defaultAudioUrl;
  }

  Future<bool> setAudioUrl(String url) {
    return _prefs.setString(_keyAudioUrl, url);
  }

  // Jitter Buffer Size
  int get jitterBufferSize {
    return _prefs.getInt(_keyJitterBufferSize) ?? AppConstants.maxJitterBufferSize;
  }

  Future<bool> setJitterBufferSize(int size) {
    return _prefs.setInt(_keyJitterBufferSize, size);
  }

  // Sync Interval
  int get syncInterval {
    return _prefs.getInt(_keySyncInterval) ?? AppConstants.syncIntervalMs;
  }

  Future<bool> setSyncInterval(int interval) {
    return _prefs.setInt(_keySyncInterval, interval);
  }

  // Ultrasonic Frequency
  int get ultrasonicFrequency {
    return _prefs.getInt(_keyUltrasonicFrequency) ?? AppConstants.ultrasonicFrequency;
  }

  Future<bool> setUltrasonicFrequency(int frequency) {
    return _prefs.setInt(_keyUltrasonicFrequency, frequency);
  }

  // Encryption Enabled
  bool get encryptionEnabled {
    return _prefs.getBool(_keyEncryptionEnabled) ?? true;
  }

  Future<bool> setEncryptionEnabled(bool enabled) {
    return _prefs.setBool(_keyEncryptionEnabled, enabled);
  }

  // Auto Reconnect
  bool get autoReconnect {
    return _prefs.getBool(_keyAutoReconnect) ?? true;
  }

  Future<bool> setAutoReconnect(bool enabled) {
    return _prefs.setBool(_keyAutoReconnect, enabled);
  }

  // Dark Mode
  bool get darkMode {
    return _prefs.getBool(_keyDarkMode) ?? false;
  }

  Future<bool> setDarkMode(bool enabled) {
    return _prefs.setBool(_keyDarkMode, enabled);
  }

  // Device Name
  String get deviceName {
    return _prefs.getString(_keyDeviceName) ?? 'Beatsync Device';
  }

  Future<bool> setDeviceName(String name) {
    return _prefs.setString(_keyDeviceName, name);
  }

  // Hotspot SSID
  String get hotspotSSID {
    return _prefs.getString(_keyHotspotSSID) ?? AppConstants.hotspotSSID;
  }

  Future<bool> setHotspotSSID(String ssid) {
    return _prefs.setString(_keyHotspotSSID, ssid);
  }

  // Hotspot Password
  String get hotspotPassword {
    return _prefs.getString(_keyHotspotPassword) ?? AppConstants.hotspotPassword;
  }

  Future<bool> setHotspotPassword(String password) {
    return _prefs.setString(_keyHotspotPassword, password);
  }

  // Audio URLs History
  List<String> get audioUrlHistory {
    return _prefs.getStringList('audio_url_history') ?? AppConstants.sampleAudioUrls;
  }

  Future<bool> addToAudioUrlHistory(String url) {
    final history = audioUrlHistory;
    if (!history.contains(url)) {
      history.insert(0, url);
      if (history.length > 10) {
        history.removeLast();
      }
      return _prefs.setStringList('audio_url_history', history);
    }
    return Future.value(true);
  }

  // Clear all settings
  Future<bool> clearAllSettings() {
    return _prefs.clear();
  }
}
